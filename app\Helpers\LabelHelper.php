<?php

namespace App\Helpers;

class LabelHelper
{
    /**
     * Labels pré-definidas disponíveis no sistema
     */
    public static function getAvailableLabels(): array
    {
        return [
            [
                'name' => 'VIP',
                'color' => 'gold',
                'icon' => 'star',
                'fa_icon' => 'fa-star',
                'description' => 'Usuário VIP com benefícios especiais'
            ],
            [
                'name' => 'Moderador',
                'color' => 'blue',
                'icon' => 'shield-check',
                'fa_icon' => 'fa-shield',
                'description' => 'Moderador da comunidade'
            ],
            [
                'name' => 'Verificado',
                'color' => 'green',
                'icon' => 'check-badge',
                'fa_icon' => 'fa-check',
                'description' => 'Perfil verificado'
            ],
            [
                'name' => 'Criador',
                'color' => 'purple',
                'icon' => 'sparkles',
                'fa_icon' => 'fa-magic',
                'description' => 'Criador de conteúdo'
            ],
            [
                'name' => 'Beta Tester',
                'color' => 'orange',
                'icon' => 'beaker',
                'fa_icon' => 'fa-flask',
                'description' => 'Testador de funcionalidades beta'
            ],
            [
                'name' => 'Influencer',
                'color' => 'pink',
                'icon' => 'megaphone',
                'fa_icon' => 'fa-bullhorn',
                'description' => 'Influenciador da plataforma'
            ],
            [
                'name' => 'Premium',
                'color' => 'red',
                'icon' => 'fire',
                'fa_icon' => 'fa-fire',
                'description' => 'Usuário premium'
            ],
            [
                'name' => 'Mentor',
                'color' => 'teal',
                'icon' => 'academic-cap',
                'fa_icon' => 'fa-graduation-cap',
                'description' => 'Mentor da comunidade'
            ],
            [
                'name' => 'Champion',
                'color' => 'yellow',
                'icon' => 'trophy',
                'fa_icon' => 'fa-trophy',
                'description' => 'Campeão de eventos'
            ],
            [
                'name' => 'Developer',
                'color' => 'silver',
                'icon' => 'cog-6-tooth',
                'fa_icon' => 'fa-cog',
                'description' => 'Desenvolvedor'
            ],
            [
                'name' => 'Pioneer',
                'color' => 'blue-dark',
                'icon' => 'rocket-launch',
                'fa_icon' => 'fa-rocket',
                'description' => 'Pioneiro da plataforma'
            ],
            [
                'name' => 'Eco',
                'color' => 'green-dark',
                'icon' => 'leaf',
                'fa_icon' => 'fa-leaf',
                'description' => 'Defensor do meio ambiente'
            ]
        ];
    }

    /**
     * Buscar uma label específica por nome
     */
    public static function getLabelByName(string $name): ?array
    {
        $labels = self::getAvailableLabels();
        
        foreach ($labels as $label) {
            if ($label['name'] === $name) {
                return $label;
            }
        }
        
        return null;
    }

    /**
     * Verificar se uma label existe
     */
    public static function labelExists(string $name): bool
    {
        return self::getLabelByName($name) !== null;
    }

    /**
     * Obter cores disponíveis das labels
     */
    public static function getAvailableColors(): array
    {
        $labels = self::getAvailableLabels();
        $colors = [];
        
        foreach ($labels as $label) {
            if (!in_array($label['color'], $colors)) {
                $colors[] = $label['color'];
            }
        }
        
        return $colors;
    }
}
