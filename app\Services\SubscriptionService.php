<?php

namespace App\Services;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\VipSubscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\IncompletePayment;

class SubscriptionService
{
    /**
     * Create a new subscription for a user
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, array $options = [])
    {
        try {
            DB::beginTransaction();

            // Ensure user is a Stripe customer
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer([
                    'name' => $user->name,
                    'email' => $user->email,
                ]);
            }

            // Create the subscription in Stripe
            $subscriptionBuilder = $user->newSubscription('default', $plan->stripe_price_id);

            // Add trial period if specified
            if ($plan->trial_period_days > 0 && !isset($options['skip_trial'])) {
                $subscriptionBuilder->trialDays($plan->trial_period_days);
            }

            // Add any additional options
            if (isset($options['coupon'])) {
                $subscriptionBuilder->withCoupon($options['coupon']);
            }

            if (isset($options['metadata'])) {
                $subscriptionBuilder->withMetadata($options['metadata']);
            }

            // Create the subscription
            $stripeSubscription = $subscriptionBuilder->create($options['payment_method'] ?? null, [
                'expand' => ['latest_invoice.payment_intent'],
            ]);

            // Create local subscription record
            $vipSubscription = VipSubscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_days' => $this->calculatePlanDays($plan),
                'amount' => $plan->price,
                'status' => $this->mapStripeStatus($stripeSubscription->status),
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $user->stripe_id,
                'activated_at' => $stripeSubscription->status === 'active' ? Carbon::now() : null,
                'trial_ends_at' => $stripeSubscription->trial_end ? Carbon::createFromTimestamp($stripeSubscription->trial_end) : null,
                'expires_at' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            ]);

            // Update user role if subscription is active
            if ($stripeSubscription->status === 'active' || $stripeSubscription->status === 'trialing') {
                $user->update(['role' => $plan->user_role]);
            }

            DB::commit();

            Log::info('Subscription created successfully', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'status' => $stripeSubscription->status,
            ]);

            return [
                'subscription' => $vipSubscription,
                'stripe_subscription' => $stripeSubscription,
                'requires_action' => $stripeSubscription->status === 'incomplete',
            ];

        } catch (IncompletePayment $e) {
            DB::rollBack();
            
            Log::warning('Subscription payment incomplete', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'subscription' => null,
                'stripe_subscription' => null,
                'requires_action' => true,
                'payment_intent' => $e->payment->id,
                'error' => 'Payment requires additional action',
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create subscription', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(VipSubscription $subscription, bool $immediately = false)
    {
        try {
            $user = $subscription->user;
            $stripeSubscription = $user->subscription('default');

            if ($stripeSubscription && $stripeSubscription->stripe_id === $subscription->stripe_subscription_id) {
                if ($immediately) {
                    $stripeSubscription->cancelNow();
                    $subscription->update([
                        'status' => VipSubscription::STATUS_CANCELED,
                        'canceled_at' => Carbon::now(),
                        'ends_at' => Carbon::now(),
                    ]);
                } else {
                    $stripeSubscription->cancel();
                    $subscription->update([
                        'status' => VipSubscription::STATUS_CANCELED,
                        'canceled_at' => Carbon::now(),
                        'ends_at' => Carbon::createFromTimestamp($stripeSubscription->asStripeSubscription()->current_period_end),
                    ]);
                }

                Log::info('Subscription canceled', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                    'immediately' => $immediately,
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Resume a canceled subscription
     */
    public function resumeSubscription(VipSubscription $subscription)
    {
        try {
            $user = $subscription->user;
            $stripeSubscription = $user->subscription('default');

            if ($stripeSubscription && $stripeSubscription->stripe_id === $subscription->stripe_subscription_id) {
                $stripeSubscription->resume();
                
                $subscription->update([
                    'status' => VipSubscription::STATUS_ACTIVE,
                    'canceled_at' => null,
                    'ends_at' => null,
                ]);

                Log::info('Subscription resumed', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to resume subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Map Stripe subscription status to local status
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'active' => VipSubscription::STATUS_ACTIVE,
            'trialing' => VipSubscription::STATUS_TRIALING,
            'canceled' => VipSubscription::STATUS_CANCELED,
            'incomplete' => VipSubscription::STATUS_PENDING,
            'incomplete_expired' => VipSubscription::STATUS_EXPIRED,
            'past_due' => VipSubscription::STATUS_PAST_DUE,
            'unpaid' => VipSubscription::STATUS_UNPAID,
            default => VipSubscription::STATUS_PENDING,
        };
    }

    /**
     * Calculate plan days based on interval
     */
    private function calculatePlanDays(SubscriptionPlan $plan): int
    {
        return match ($plan->interval) {
            'day' => $plan->interval_count,
            'week' => $plan->interval_count * 7,
            'month' => $plan->interval_count * 30,
            'year' => $plan->interval_count * 365,
            default => 30,
        };
    }

    /**
     * Sync subscription status with Stripe
     */
    public function syncSubscriptionStatus(VipSubscription $subscription)
    {
        try {
            $user = $subscription->user;
            $stripeSubscription = $user->subscription('default');

            if ($stripeSubscription && $stripeSubscription->stripe_id === $subscription->stripe_subscription_id) {
                $stripeData = $stripeSubscription->asStripeSubscription();
                
                $subscription->update([
                    'status' => $this->mapStripeStatus($stripeData->status),
                    'expires_at' => Carbon::createFromTimestamp($stripeData->current_period_end),
                    'trial_ends_at' => $stripeData->trial_end ? Carbon::createFromTimestamp($stripeData->trial_end) : null,
                ]);

                // Update user role based on subscription status
                if (in_array($stripeData->status, ['active', 'trialing'])) {
                    $user->update(['role' => $subscription->plan->user_role]);
                } else {
                    $user->update(['role' => 'visitante']);
                }

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to sync subscription status', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
