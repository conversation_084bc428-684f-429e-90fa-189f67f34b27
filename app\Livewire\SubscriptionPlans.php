<?php

namespace App\Livewire;

use App\Models\SubscriptionPlan;
use App\Models\VipSubscription;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class SubscriptionPlans extends Component
{
    public $plans = [];
    public $currentSubscription = null;
    public $selectedPlan = null;
    public $showCancelModal = false;
    public $showResumeModal = false;

    public function mount()
    {
        $this->loadPlans();
        $this->loadCurrentSubscription();
    }

    public function loadPlans()
    {
        $this->plans = SubscriptionPlan::active()
            ->ordered()
            ->get()
            ->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'description' => $plan->description,
                    'price' => $plan->price,
                    'formatted_price' => $plan->formatted_price,
                    'interval_description' => $plan->interval_description,
                    'monthly_price' => $plan->monthly_price,
                    'discount_percentage' => $plan->discount_percentage,
                    'features' => $plan->features ?? [],
                    'benefits' => $plan->benefits ?? [],
                    'trial_period_days' => $plan->trial_period_days,
                    'has_trial' => $plan->has_trial,
                    'recommended' => $plan->recommended,
                    'popular' => $plan->popular,
                    'is_available' => $plan->is_available,
                ];
            })
            ->toArray();
    }

    public function loadCurrentSubscription()
    {
        if (Auth::check()) {
            $subscription = Auth::user()->activeVipSubscription;
            
            if ($subscription) {
                $this->currentSubscription = [
                    'id' => $subscription->id,
                    'plan_name' => $subscription->plan->name ?? 'Plano VIP',
                    'status' => $subscription->status,
                    'is_active' => $subscription->isActive(),
                    'is_canceled' => $subscription->isCanceled(),
                    'on_trial' => $subscription->onTrial(),
                    'remaining_days' => $subscription->getRemainingDays(),
                    'remaining_trial_days' => $subscription->getRemainingTrialDays(),
                    'expires_at' => $subscription->expires_at?->format('d/m/Y'),
                    'trial_ends_at' => $subscription->trial_ends_at?->format('d/m/Y'),
                    'canceled_at' => $subscription->canceled_at?->format('d/m/Y'),
                    'ends_at' => $subscription->ends_at?->format('d/m/Y'),
                ];
            }
        }
    }

    public function selectPlan($planSlug)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Check if user already has an active subscription
        if ($this->currentSubscription && $this->currentSubscription['is_active']) {
            $this->dispatch('notify', [
                'message' => 'Você já possui uma assinatura VIP ativa.',
                'type' => 'info'
            ]);
            return;
        }

        $this->selectedPlan = $planSlug;
        
        // Redirect to checkout
        return redirect()->route('vip.checkout', ['plan_slug' => $planSlug]);
    }

    public function showCancelSubscription()
    {
        if (!$this->currentSubscription || !$this->currentSubscription['is_active']) {
            $this->dispatch('notify', [
                'message' => 'Nenhuma assinatura ativa encontrada.',
                'type' => 'error'
            ]);
            return;
        }

        $this->showCancelModal = true;
    }

    public function cancelSubscription()
    {
        $this->showCancelModal = false;
        
        // Redirect to cancel route
        return redirect()->route('vip.cancel');
    }

    public function showResumeSubscription()
    {
        if (!$this->currentSubscription || !$this->currentSubscription['is_canceled']) {
            $this->dispatch('notify', [
                'message' => 'Nenhuma assinatura cancelada encontrada.',
                'type' => 'error'
            ]);
            return;
        }

        $this->showResumeModal = true;
    }

    public function resumeSubscription()
    {
        $this->showResumeModal = false;
        
        // Redirect to resume route
        return redirect()->route('vip.resume');
    }

    public function closeModal()
    {
        $this->showCancelModal = false;
        $this->showResumeModal = false;
    }

    public function getFeatureIcon($feature)
    {
        $icons = [
            'unlimited_access' => 'clock',
            'profile_visitors' => 'eye',
            'unlimited_messages' => 'chat-bubble-left-right',
            'radar_boost' => 'signal',
            'priority_support' => 'shield-check',
            'vip_support' => 'star',
            'priority_events' => 'calendar',
            'shop_discount' => 'tag',
            'wallet_credits' => 'banknotes',
        ];

        return $icons[$feature] ?? 'check';
    }

    public function getBenefitDescription($benefit, $value)
    {
        $descriptions = [
            'unlimited_access' => 'Acesso ilimitado sem restrição de tempo',
            'profile_visitors' => 'Veja quem visitou seu perfil',
            'unlimited_messages' => 'Envie mensagens ilimitadas',
            'radar_boost' => 'Destaque no radar de usuários',
            'priority_support' => 'Suporte prioritário',
            'vip_support' => 'Suporte VIP exclusivo',
            'priority_events' => 'Acesso prioritário a eventos',
            'shop_discount' => "Desconto de {$value}% na loja",
            'wallet_credits' => "R$ {$value} em créditos na carteira",
            'badge_vip' => 'Badge VIP no perfil',
            'badge_quarterly' => 'Badge Membro Trimestral',
            'badge_biannual' => 'Badge Membro Semestral',
            'badge_gold' => 'Badge VIP Gold exclusivo',
        ];

        return $descriptions[$benefit] ?? $benefit;
    }

    public function render()
    {
        return view('livewire.subscription-plans');
    }
}
