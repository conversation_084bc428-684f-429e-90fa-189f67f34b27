<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class NotificationSettings extends Component
{
    public $soundEnabled = true;
    public $browserNotificationsEnabled = false;
    public $vibrationEnabled = true;
    public $doNotDisturbMode = false;
    public $doNotDisturbStart = '22:00';
    public $doNotDisturbEnd = '08:00';
    public $soundVolume = 0.5;
    public $showPreview = true;
    public $groupNotifications = true;
    public $maxNotifications = 5;
    
    // Tipos de notificação
    public $messageNotifications = true;
    public $mentionNotifications = true;
    public $likeNotifications = true;
    public $followNotifications = true;
    public $systemNotifications = true;

    public function mount()
    {
        $this->loadUserSettings();
    }

    public function loadUserSettings()
    {
        try {
            $user = Auth::user();
            if (!$user) return;

            // Carregar configurações do usuário ou usar padrões
            $this->soundEnabled = $user->notification_sound ?? true;
            $this->browserNotificationsEnabled = $user->browser_notifications ?? false;
            $this->vibrationEnabled = $user->notification_vibration ?? true;
            $this->doNotDisturbMode = $user->do_not_disturb ?? false;
            $this->doNotDisturbStart = $user->dnd_start_time ?? '22:00';
            $this->doNotDisturbEnd = $user->dnd_end_time ?? '08:00';
            $this->soundVolume = $user->notification_volume ?? 0.5;
            $this->showPreview = $user->notification_preview ?? true;
            $this->groupNotifications = $user->group_notifications ?? true;
            $this->maxNotifications = $user->max_notifications ?? 5;

            // Tipos de notificação
            $this->messageNotifications = $user->notify_messages ?? true;
            $this->mentionNotifications = $user->notify_mentions ?? true;
            $this->likeNotifications = $user->notify_likes ?? true;
            $this->followNotifications = $user->notify_follows ?? true;
            $this->systemNotifications = $user->notify_system ?? true;
        } catch (\Exception $e) {
            logger()->error('Erro ao carregar configurações de notificação: ' . $e->getMessage());
            // Usar valores padrão em caso de erro
            $this->soundEnabled = true;
            $this->browserNotificationsEnabled = false;
            $this->vibrationEnabled = true;
        }
    }

    public function saveSettings()
    {
        $user = Auth::user();
        if (!$user) return;

        $user->update([
            'notification_sound' => $this->soundEnabled,
            'browser_notifications' => $this->browserNotificationsEnabled,
            'notification_vibration' => $this->vibrationEnabled,
            'do_not_disturb' => $this->doNotDisturbMode,
            'dnd_start_time' => $this->doNotDisturbStart,
            'dnd_end_time' => $this->doNotDisturbEnd,
            'notification_volume' => $this->soundVolume,
            'notification_preview' => $this->showPreview,
            'group_notifications' => $this->groupNotifications,
            'max_notifications' => $this->maxNotifications,
            'notify_messages' => $this->messageNotifications,
            'notify_mentions' => $this->mentionNotifications,
            'notify_likes' => $this->likeNotifications,
            'notify_follows' => $this->followNotifications,
            'notify_system' => $this->systemNotifications,
        ]);

        // Dispatch evento para atualizar configurações no frontend
        $this->dispatch('notification-settings-updated', [
            'soundEnabled' => $this->soundEnabled,
            'browserNotificationsEnabled' => $this->browserNotificationsEnabled,
            'vibrationEnabled' => $this->vibrationEnabled,
            'doNotDisturbMode' => $this->doNotDisturbMode,
            'doNotDisturbStart' => $this->doNotDisturbStart,
            'doNotDisturbEnd' => $this->doNotDisturbEnd,
            'soundVolume' => $this->soundVolume,
            'showPreview' => $this->showPreview,
            'groupNotifications' => $this->groupNotifications,
            'maxNotifications' => $this->maxNotifications,
            'notificationTypes' => [
                'message' => $this->messageNotifications,
                'mention' => $this->mentionNotifications,
                'like' => $this->likeNotifications,
                'follow' => $this->followNotifications,
                'system' => $this->systemNotifications,
            ]
        ]);

        // Mostrar toast de sucesso
        $this->dispatch('toast', [
            'message' => 'Configurações de notificação salvas com sucesso!',
            'type' => 'success',
            'timeout' => 3000
        ]);
    }

    public function testNotification($type = 'message')
    {
        $messages = [
            'message' => 'Esta é uma mensagem de teste',
            'mention' => 'Você foi mencionado em uma postagem',
            'like' => 'Alguém curtiu sua postagem',
            'follow' => 'Você tem um novo seguidor',
            'system' => 'Notificação do sistema'
        ];

        $this->dispatch('test-notification', [
            'message' => $messages[$type] ?? $messages['message'],
            'type' => $type,
            'timeout' => 5000,
            'avatar' => Auth::user()->currentPhoto ? 
                       \Illuminate\Support\Facades\Storage::url(Auth::user()->currentPhoto->photo_path) : 
                       asset('images/users/avatar.svg'),
            'sender_id' => Auth::id()
        ]);
    }

    public function toggleDoNotDisturb()
    {
        $this->doNotDisturbMode = !$this->doNotDisturbMode;
        $this->saveSettings();
    }

    public function requestBrowserPermission()
    {
        // Dispatch evento para solicitar permissão do browser
        $this->dispatch('request-browser-permission');
    }

    public function resetToDefaults()
    {
        $this->soundEnabled = true;
        $this->browserNotificationsEnabled = false;
        $this->vibrationEnabled = true;
        $this->doNotDisturbMode = false;
        $this->doNotDisturbStart = '22:00';
        $this->doNotDisturbEnd = '08:00';
        $this->soundVolume = 0.5;
        $this->showPreview = true;
        $this->groupNotifications = true;
        $this->maxNotifications = 5;
        $this->messageNotifications = true;
        $this->mentionNotifications = true;
        $this->likeNotifications = true;
        $this->followNotifications = true;
        $this->systemNotifications = true;

        $this->saveSettings();

        $this->dispatch('toast', [
            'message' => 'Configurações restauradas para os padrões',
            'type' => 'info',
            'timeout' => 3000
        ]);
    }

    public function getNotificationTypesProperty()
    {
        return [
            'message' => [
                'label' => 'Mensagens',
                'icon' => 'chat-bubble-left-right',
                'description' => 'Notificações de novas mensagens',
                'enabled' => $this->messageNotifications
            ],
            'mention' => [
                'label' => 'Menções',
                'icon' => 'at-symbol',
                'description' => 'Quando você é mencionado',
                'enabled' => $this->mentionNotifications
            ],
            'like' => [
                'label' => 'Curtidas',
                'icon' => 'heart',
                'description' => 'Curtidas em suas postagens',
                'enabled' => $this->likeNotifications
            ],
            'follow' => [
                'label' => 'Seguidores',
                'icon' => 'user-plus',
                'description' => 'Novos seguidores',
                'enabled' => $this->followNotifications
            ],
            'system' => [
                'label' => 'Sistema',
                'icon' => 'cog-6-tooth',
                'description' => 'Notificações do sistema',
                'enabled' => $this->systemNotifications
            ]
        ];
    }

    public function render()
    {
        return view('livewire.notification-settings');
    }
}
