<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Solicitação de Entrada - {{ $group->name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #E60073;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #E60073;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .user-info {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .user-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .group-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #00FFF7;
        }
        .group-name {
            font-size: 18px;
            font-weight: bold;
            color: #E60073;
            margin-bottom: 10px;
        }
        .group-description {
            color: #666;
            margin-bottom: 10px;
        }
        .group-stats {
            font-size: 14px;
            color: #888;
        }
        .group-stats span {
            margin-right: 15px;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-approve {
            background: linear-gradient(135deg, #00FFF7, #E60073);
            color: white;
        }
        .btn-approve:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(230, 0, 115, 0.3);
        }
        .btn-view {
            background: #6c757d;
            color: white;
        }
        .btn-view:hover {
            background: #5a6268;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ config('app.name') }}</div>
            <h1 style="color: #333; margin: 0;">Nova Solicitação de Entrada</h1>
        </div>

        <div class="content">
            <p>Olá <strong>{{ $admin->name }}</strong>,</p>
            
            <p>Você recebeu uma nova solicitação de entrada para o grupo que você administra!</p>

            <div class="user-info">
                <div class="user-name">{{ $requestingUser->name }}</div>
                <div>@{{ $requestingUser->username }}</div>
            </div>

            <div class="group-info">
                <div class="group-name">{{ $group->name }}</div>
                @if($group->description)
                    <div class="group-description">{{ $group->description }}</div>
                @endif
                <div class="group-stats">
                    <span>👥 {{ $group->members_count }} {{ $group->members_count == 1 ? 'membro' : 'membros' }}</span>
                    <span>🔒 {{ $group->privacy == 'public' ? 'Público' : ($group->privacy == 'private' ? 'Privado' : 'Secreto') }}</span>
                </div>
            </div>

            <p>Como administrador do grupo, você pode aprovar ou rejeitar esta solicitação:</p>

            <div class="button-container">
                <a href="{{ route('grupos.show', $group->slug) }}" class="btn btn-approve">
                    ✅ Gerenciar Solicitação
                </a>
                <a href="{{ route('user.profile', $requestingUser->username) }}" class="btn btn-view">
                    👤 Ver Perfil
                </a>
            </div>

            <p style="font-size: 14px; color: #666; margin-top: 30px;">
                <strong>Nota:</strong> Esta solicitação ficará pendente até que você tome uma decisão. 
                Você pode gerenciar todas as solicitações pendentes na seção de membros do grupo.
            </p>
        </div>

        <div class="footer">
            <p>Este é um email automático do sistema {{ config('app.name') }}.</p>
            <p>Se você não deseja mais receber estas notificações, ajuste suas preferências no seu perfil.</p>
        </div>
    </div>
</body>
</html>
