<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-300 mb-4">Planos VIP</h1>
        <p class="text-xl text-gray-400 max-w-3xl mx-auto">
            Desbloqueie todo o potencial da plataforma com nossos planos VIP. 
            Acesso ilimitado, recursos exclusivos e muito mais.
        </p>
    </div>

    <!-- Current Subscription Status -->
    @if($currentSubscription)
        <div class="mb-8 bg-zinc-800 border border-zinc-700 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-300 mb-2">Sua Assinatura Atual</h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-2xl font-bold text-[#E60073]">{{ $currentSubscription['plan_name'] }}</span>
                        
                        @if($currentSubscription['is_active'])
                            <flux:badge color="green" size="sm">Ativa</flux:badge>
                        @elseif($currentSubscription['is_canceled'])
                            <flux:badge color="yellow" size="sm">Cancelada</flux:badge>
                        @else
                            <flux:badge color="gray" size="sm">{{ ucfirst($currentSubscription['status']) }}</flux:badge>
                        @endif
                    </div>
                    
                    @if($currentSubscription['on_trial'])
                        <p class="text-sm text-gray-400 mt-1">
                            Trial termina em {{ $currentSubscription['remaining_trial_days'] }} dias ({{ $currentSubscription['trial_ends_at'] }})
                        </p>
                    @elseif($currentSubscription['is_active'])
                        <p class="text-sm text-gray-400 mt-1">
                            Renova em {{ $currentSubscription['remaining_days'] }} dias ({{ $currentSubscription['expires_at'] }})
                        </p>
                    @elseif($currentSubscription['is_canceled'])
                        <p class="text-sm text-gray-400 mt-1">
                            Acesso até {{ $currentSubscription['ends_at'] }}
                        </p>
                    @endif
                </div>
                
                <div class="flex space-x-3">
                    @if($currentSubscription['is_active'] && !$currentSubscription['is_canceled'])
                        <flux:button color="red" size="sm" wire:click="showCancelSubscription">
                            Cancelar Assinatura
                        </flux:button>
                    @elseif($currentSubscription['is_canceled'])
                        <flux:button color="green" size="sm" wire:click="showResumeSubscription">
                            Reativar Assinatura
                        </flux:button>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Plans Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        @foreach($plans as $plan)
            <div class="relative bg-zinc-800 border border-zinc-700 rounded-lg p-6 hover:border-[#E60073] transition-colors duration-200
                {{ $plan['recommended'] ? 'ring-2 ring-[#E60073]' : '' }}
                {{ $plan['popular'] ? 'border-[#00FFF7]' : '' }}">
                
                <!-- Popular/Recommended Badge -->
                @if($plan['recommended'])
                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <flux:badge color="pink" size="sm">Recomendado</flux:badge>
                    </div>
                @elseif($plan['popular'])
                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <flux:badge color="cyan" size="sm">Popular</flux:badge>
                    </div>
                @endif

                <!-- Plan Header -->
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-gray-300 mb-2">{{ $plan['name'] }}</h3>
                    <div class="mb-2">
                        <span class="text-3xl font-bold text-[#E60073]">{{ $plan['formatted_price'] }}</span>
                        <span class="text-gray-400">/ {{ $plan['interval_description'] }}</span>
                    </div>
                    
                    @if($plan['discount_percentage'] > 0)
                        <div class="text-sm text-[#00FFF7] mb-2">
                            {{ $plan['discount_percentage'] }}% de desconto
                        </div>
                        <div class="text-xs text-gray-400">
                            Equivale a R$ {{ number_format($plan['monthly_price'], 2, ',', '.') }}/mês
                        </div>
                    @endif

                    @if($plan['has_trial'])
                        <div class="text-sm text-[#FFE600] mt-2">
                            {{ $plan['trial_period_days'] }} dias grátis
                        </div>
                    @endif
                </div>

                <!-- Plan Description -->
                @if($plan['description'])
                    <p class="text-gray-400 text-sm text-center mb-6">{{ $plan['description'] }}</p>
                @endif

                <!-- Features List -->
                <div class="space-y-3 mb-6">
                    @foreach($plan['features'] as $feature)
                        <div class="flex items-center text-sm text-gray-300">
                            <flux:icon name="check" class="w-4 h-4 text-[#00FFF7] mr-3 flex-shrink-0" />
                            <span>{{ $feature }}</span>
                        </div>
                    @endforeach
                </div>

                <!-- Benefits -->
                @if(!empty($plan['benefits']))
                    <div class="border-t border-zinc-700 pt-4 mb-6">
                        <h4 class="text-sm font-semibold text-gray-300 mb-3">Benefícios Inclusos:</h4>
                        <div class="space-y-2">
                            @foreach($plan['benefits'] as $benefit => $value)
                                @if($value === true || (is_numeric($value) && $value > 0))
                                    <div class="flex items-center text-xs text-gray-400">
                                        <flux:icon name="{{ $this->getFeatureIcon($benefit) }}" class="w-3 h-3 text-[#FFE600] mr-2 flex-shrink-0" />
                                        <span>{{ $this->getBenefitDescription($benefit, $value) }}</span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Action Button -->
                <div class="text-center">
                    @if(!$plan['is_available'])
                        <flux:button disabled class="w-full">
                            Indisponível
                        </flux:button>
                    @elseif($currentSubscription && $currentSubscription['is_active'])
                        <flux:button disabled class="w-full">
                            Assinatura Ativa
                        </flux:button>
                    @else
                        <flux:button 
                            wire:click="selectPlan('{{ $plan['slug'] }}')" 
                            class="w-full"
                            color="{{ $plan['recommended'] ? 'pink' : ($plan['popular'] ? 'cyan' : 'zinc') }}">
                            @if($plan['has_trial'])
                                Começar Trial Grátis
                            @else
                                Assinar Agora
                            @endif
                        </flux:button>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    <!-- FAQ Section -->
    <div class="bg-zinc-800 border border-zinc-700 rounded-lg p-8">
        <h2 class="text-2xl font-bold text-gray-300 mb-6 text-center">Perguntas Frequentes</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-gray-300 mb-2">Posso cancelar a qualquer momento?</h3>
                <p class="text-gray-400 text-sm">Sim, você pode cancelar sua assinatura a qualquer momento. Você continuará tendo acesso VIP até o final do período pago.</p>
            </div>
            
            <div>
                <h3 class="font-semibold text-gray-300 mb-2">Como funciona o período de trial?</h3>
                <p class="text-gray-400 text-sm">Durante o trial, você tem acesso completo a todos os recursos VIP. Se não cancelar antes do fim do trial, será cobrado automaticamente.</p>
            </div>
            
            <div>
                <h3 class="font-semibold text-gray-300 mb-2">Posso trocar de plano?</h3>
                <p class="text-gray-400 text-sm">Entre em contato com nosso suporte para alterar seu plano. Faremos o ajuste proporcional do valor.</p>
            </div>
            
            <div>
                <h3 class="font-semibold text-gray-300 mb-2">Os créditos da carteira expiram?</h3>
                <p class="text-gray-400 text-sm">Não, os créditos adicionados à sua carteira não expiram e podem ser usados a qualquer momento na plataforma.</p>
            </div>
        </div>
    </div>

    <!-- Cancel Subscription Modal -->
    @if($showCancelModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Cancelar Assinatura</h3>
                <p class="text-gray-400 mb-6">
                    Tem certeza que deseja cancelar sua assinatura? Você continuará tendo acesso VIP até o final do período pago.
                </p>
                <div class="flex space-x-3">
                    <flux:button color="red" wire:click="cancelSubscription" class="flex-1">
                        Sim, Cancelar
                    </flux:button>
                    <flux:button color="zinc" wire:click="closeModal" class="flex-1">
                        Não, Manter
                    </flux:button>
                </div>
            </div>
        </div>
    @endif

    <!-- Resume Subscription Modal -->
    @if($showResumeModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Reativar Assinatura</h3>
                <p class="text-gray-400 mb-6">
                    Deseja reativar sua assinatura VIP? Você voltará a ter acesso a todos os recursos premium.
                </p>
                <div class="flex space-x-3">
                    <flux:button color="green" wire:click="resumeSubscription" class="flex-1">
                        Sim, Reativar
                    </flux:button>
                    <flux:button color="zinc" wire:click="closeModal" class="flex-1">
                        Cancelar
                    </flux:button>
                </div>
            </div>
        </div>
    @endif
</div>
