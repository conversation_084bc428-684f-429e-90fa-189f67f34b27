<?php

namespace App\Helpers;

use App\Models\User;
use App\Models\UserPoint;

class RankingHelper
{
    /**
     * Get the ranking border class for a user based on their position
     *
     * @param User $user
     * @return string
     */
    public static function getRankingBorderClass(User $user): string
    {
        $position = self::getUserRankingPosition($user);
        
        if ($position === null) {
            return '';
        }
        
        return match (true) {
            $position === 1 => 'crown',
            $position === 2 => 'diamond',
            $position === 3 => 'gold',
            $position >= 4 && $position <= 10 => 'silver',
            $position >= 11 && $position <= 20 => 'bronze',
            default => ''
        };
    }
    
    /**
     * Get the ranking position for a user
     *
     * @param User $user
     * @return int|null
     */
    public static function getUserRankingPosition(User $user): ?int
    {
        $userPoints = UserPoint::where('user_id', $user->id)->first();
        
        if (!$userPoints || $userPoints->total_points <= 0) {
            return null;
        }
        
        $position = UserPoint::where('total_points', '>', $userPoints->total_points)->count() + 1;
        
        return $position;
    }
    
    /**
     * Get ranking position by user ID (for performance when user object is not available)
     *
     * @param int $userId
     * @return int|null
     */
    public static function getUserRankingPositionById(int $userId): ?int
    {
        $userPoints = UserPoint::where('user_id', $userId)->first();
        
        if (!$userPoints || $userPoints->total_points <= 0) {
            return null;
        }
        
        $position = UserPoint::where('total_points', '>', $userPoints->total_points)->count() + 1;
        
        return $position;
    }
    
    /**
     * Get the ranking border class by user ID
     *
     * @param int $userId
     * @return string
     */
    public static function getRankingBorderClassById(int $userId): string
    {
        $position = self::getUserRankingPositionById($userId);
        
        if ($position === null) {
            return '';
        }
        
        return match (true) {
            $position === 1 => 'crown',
            $position === 2 => 'diamond',
            $position === 3 => 'gold',
            $position >= 4 && $position <= 10 => 'silver',
            $position >= 11 && $position <= 20 => 'bronze',
            default => ''
        };
    }
    
    /**
     * Get ranking info for display
     *
     * @param User $user
     * @return array
     */
    public static function getRankingInfo(User $user): array
    {
        $position = self::getUserRankingPosition($user);
        $borderClass = self::getRankingBorderClass($user);
        
        $rankingTier = match (true) {
            $position === 1 => 'Rei/Rainha',
            $position === 2 => 'Diamante',
            $position === 3 => 'Ouro',
            $position >= 4 && $position <= 10 => 'Prata',
            $position >= 11 && $position <= 20 => 'Bronze',
            default => null
        };
        
        return [
            'position' => $position,
            'border_class' => $borderClass,
            'tier' => $rankingTier,
            'has_border' => !empty($borderClass)
        ];
    }
    
    /**
     * Check if user should have crown (top 2 positions)
     *
     * @param User $user
     * @return bool
     */
    public static function shouldHaveCrown(User $user): bool
    {
        $position = self::getUserRankingPosition($user);
        return $position !== null && $position <= 2;
    }
    
    /**
     * Get the complete avatar HTML with ranking border
     *
     * @param User $user
     * @param string $avatarUrl
     * @param string $additionalClasses
     * @param string $size (sm, md, lg)
     * @return string
     */
    public static function getAvatarWithRankingBorder(User $user, string $avatarUrl, string $additionalClasses = '', string $size = 'md'): string
    {
        $borderClass = self::getRankingBorderClass($user);

        $sizeClasses = match ($size) {
            'sm' => 'w-8 h-8',
            'lg' => 'w-16 h-16',
            'xl' => 'w-24 h-24',
            default => 'w-10 h-10'
        };

        $containerClass = 'ranking-avatar';
        if ($borderClass) {
            $containerClass .= ' ' . $borderClass;
        }

        return sprintf(
            '<div class="%s"><img src="%s" alt="Avatar" class="%s %s rounded-full object-cover"></div>',
            $containerClass,
            $avatarUrl,
            $sizeClasses,
            $additionalClasses
        );
    }
}
