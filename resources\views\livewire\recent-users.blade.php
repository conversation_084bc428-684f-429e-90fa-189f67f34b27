<?php

use function Livewire\Volt\state;
use function Livewire\Volt\mount;
use App\Models\User;
use App\Models\FollowRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

state(['recentUsers' => []]);
state(['requestStatus' => []]);

$getRecentUsers = function () {
    $this->recentUsers = User::with(['currentPhoto'])
    ->select('id', 'name', 'username', 'last_seen')
    ->whereNotNull('last_seen')
    ->where('id', '!=', Auth::id())
    ->orderBy('last_seen', 'desc')
    ->take(10)
    ->get()
    ->map(function ($user) {
        // Check for existing follow request
        $request = FollowRequest::where('sender_id', Auth::id())
            ->where('receiver_id', $user->id)
            ->first();

        $this->requestStatus[$user->id] = $request ? $request->status : null;

        // Use the current photo if available, otherwise use default avatar
        $user->avatar_url = $user->currentPhoto
            ? Storage::url($user->currentPhoto->photo_path)
            : asset('images/users/avatar.svg'); // Usando avatar.svg como padrão

        // Format last seen
        if ($user->last_seen) {
            $lastSeenDate = Carbon::parse($user->last_seen);
            if ($lastSeenDate->diffInMinutes(now()) < 5) {
                $user->formatted_last_seen = 'Online agora';
            } elseif ($lastSeenDate->diffInMinutes(now()) < 60) {
                $user->formatted_last_seen = $lastSeenDate->diffForHumans();
            } elseif ($lastSeenDate->isToday()) {
                $user->formatted_last_seen = 'Hoje às ' . $lastSeenDate->format('H:i');
            } elseif ($lastSeenDate->isYesterday()) {
                $user->formatted_last_seen = 'Ontem às ' . $lastSeenDate->format('H:i');
            } else {
                $user->formatted_last_seen = $lastSeenDate->format('d/m/Y H:i');
            }
        } else {
            $user->formatted_last_seen = 'Nunca online';
        }

        return $user;
    })
    ->toArray();
};

$toggleFollow = function ($userId) {
    $existingRequest = FollowRequest::where('sender_id', Auth::id())
        ->where('receiver_id', $userId)
        ->first();

    if ($existingRequest) {
        $existingRequest->delete();
        $this->requestStatus[$userId] = null;
    } else {
        FollowRequest::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $userId,
            'status' => 'pending'
        ]);
        $this->requestStatus[$userId] = 'pending';
    }
};

mount(function () {
    $this->getRecentUsers();
});

?>

<div id="ultimos_acessos">
    <h3 class="text-white bg-zinc-700 p-3 rounded-t-lg font-semibold">Últimos Acessos</h3>
    <ul class="p-3 space-y-3">
        @foreach ($recentUsers as $user)
            <li class="flex items-center justify-between space-x-3">
                <div class="relative flex items-center space-x-3 flex-1">
                    <div class="relative">
                        @php
                            $recentUserRankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClassById($user['id']);
                            $recentUserShouldHaveWings = in_array($recentUserRankingBorderClass, ['crown', 'diamond']);
                        @endphp
                        <div class="ranking-avatar {{ $recentUserRankingBorderClass }}">
                            @if($recentUserShouldHaveWings)
                                <div class="wings"></div>
                            @endif
                            <img src="{{ $user['avatar_url'] }}" class="w-12 h-12 rounded-full object-cover">
                        </div>
                        <div class="absolute -top-1 -right-1">
                            <livewire:user-status-indicator :userId="$user['id']" />
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex flex-col">
                            <a href="/{{ $user['username'] }}" class="text-white hover:underline text-sm font-medium truncate">
                                {{ $user['name'] }}
                            </a>
                            <a href="/{{ $user['username'] }}" class="text-zinc-400 hover:text-zinc-300 text-xs truncate">
                                {{ '@' . $user['username'] }}
                            </a>
                            <span class="text-zinc-500 text-xs">
                                {{ $user['formatted_last_seen'] }}
                            </span>
                        </div>
                    </div>
                </div>
                @if($user['id'] !== Auth::id())
                    <button wire:click="toggleFollow({{ $user['id'] }})"
                            @class([
                                'px-3 py-1 rounded-full text-xs font-medium transition-colors flex-shrink-0',
                                'bg-yellow-500 text-gray-800 hover:bg-yellow-600' => $requestStatus[$user['id']] === 'pending',
                                'bg-gray-200 text-gray-800 cursor-not-allowed' => $requestStatus[$user['id']] === 'accepted',
                                'bg-purple-500 text-white hover:bg-purple-600' => !$requestStatus[$user['id']]
                            ])>
                        @if($requestStatus[$user['id']] === 'pending')
                            Solicitado
                        @elseif($requestStatus[$user['id']] === 'accepted')
                            Seguindo
                        @else
                            Seguir
                        @endif
                    </button>
                @endif
            </li>
        @endforeach

        @if(empty($recentUsers))
            <li class="text-center py-4">
                <span class="text-zinc-500 text-sm">Nenhum usuário online recentemente</span>
            </li>
        @endif
    </ul>
</div>
