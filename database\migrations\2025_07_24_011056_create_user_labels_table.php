<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_labels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Nome da label (ex: "VIP", "Moderador", "Verificado")
            $table->string('color')->default('blue'); // Cor da label
            $table->string('icon')->nullable(); // Ícone da label (heroicon name)
            $table->text('description')->nullable(); // Descrição da label
            $table->boolean('is_active')->default(true);
            $table->timestamp('expires_at')->nullable(); // Para labels temporárias
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null'); // Quem atribuiu
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_labels');
    }
};
