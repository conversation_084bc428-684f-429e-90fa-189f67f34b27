<div
    x-data="{
        currentIndex: {{ $currentIndex }},
        posts: @js($posts),
        isPlaying: true,
        isMuted: false,

        init() {
            this.$watch('currentIndex', (value) => {
                this.setupCurrentImage();
            });

            this.$nextTick(() => {
                this.setupCurrentImage();
                this.setupIntersectionObserver();

                // Rolar para o post inicial se especificado
                if (this.currentIndex > 0) {
                    this.scrollToImage(this.currentIndex);
                }
            });

            // Escutar eventos do Livewire
            Livewire.on('postLiked', (data) => {
                const postIndex = this.posts.findIndex(post => post.id === data.postId);
                if (postIndex !== -1) {
                    this.posts[postIndex].likes_count = data.likesCount;
                    this.posts[postIndex].liked_by_user = data.isLiked;
                }
            });

            Livewire.on('commentAdded', (data) => {
                const postIndex = this.posts.findIndex(post => post.id === data.postId);
                if (postIndex !== -1) {
                    this.posts[postIndex].comments.unshift(data.comment);
                    this.posts[postIndex].comments_count = data.commentsCount;
                }
            });
        },

        setupIntersectionObserver() {
            const images = document.querySelectorAll('.story-image');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const index = parseInt(entry.target.dataset.index);
                        if (this.currentIndex !== index) {
                            this.currentIndex = index;
                        }
                    }
                });
            }, { threshold: 0.7 });

            images.forEach(image => observer.observe(image));
        },

        setupCurrentImage() {
            // Função para configurar a imagem atual (se necessário)
            setTimeout(() => {
                const image = document.querySelector(`.story-image[data-index='${this.currentIndex}']`);
                if (image) {
                    // Pode adicionar lógica específica para imagens aqui
                }
            }, 100);
        },

        nextImage() {
            if (this.currentIndex < this.posts.length - 1) {
                this.currentIndex++;
                this.scrollToImage(this.currentIndex);
            }
        },

        prevImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
                this.scrollToImage(this.currentIndex);
            }
        },

        scrollToImage(index) {
            const imageElement = document.getElementById('story-' + this.posts[index].id);
            if (imageElement) {
                imageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        },

        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
    }"
    class="h-screen w-full bg-black overflow-hidden"
    @keydown.arrow-down.window="nextImage()"
    @keydown.arrow-up.window="prevImage()"
    @wheel.debounce.300="$event.deltaY > 0 ? nextImage() : prevImage()"
>
    <!-- Stories Container -->
    <div class="snap-y snap-mandatory h-screen w-full overflow-y-scroll">
        <template x-for="(post, index) in posts" :key="post.id">
            <div
                class="snap-start h-screen w-full relative flex items-center justify-center bg-black"
                :id="'story-' + post.id"
            >
                <!-- Image -->
                <img
                    :src="post.image"
                    class="story-image h-full w-full object-contain"
                    :data-index="index"
                    @click="nextImage()"
                />

                <!-- Loading Bar -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-white/20 z-20">
                    <div
                        class="h-full bg-white transition-all duration-300 ease-linear"
                        :style="'width: ' + ((currentIndex + 1) / posts.length * 100) + '%'"
                    ></div>
                </div>

                <!-- Overlay Content -->
                <div class="absolute inset-0 pointer-events-none">
                    <!-- Top Gradient -->
                    <div class="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-black/50 to-transparent"></div>
                    
                    <!-- Bottom Gradient -->
                    <div class="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>

                <!-- User Info (Top) -->
                <div class="absolute top-4 left-4 right-4 flex items-center justify-between z-10">
                    <div class="flex items-center space-x-3">
                        <img 
                            :src="post.user.avatar" 
                            :alt="post.user.name"
                            class="w-10 h-10 rounded-full border-2 border-white/20"
                        />
                        <div>
                            <p class="text-white font-semibold text-sm" x-text="post.user.name"></p>
                            <p class="text-white/70 text-xs" x-text="'@' + post.user.username"></p>
                        </div>
                    </div>
                    

                </div>

                <!-- Informações do usuário e descrição -->
                <div class="absolute bottom-20 left-4 right-16 pointer-events-auto">
                    <div class="flex items-center mb-2">
                        <img :src="post.user.avatar" class="w-10 h-10 rounded-full object-cover border-2 border-white" :alt="post.user.name">
                        <div class="ml-2">
                            <p class="text-white font-bold" x-text="post.user.name"></p>
                            <p class="text-gray-300 text-sm" x-text="'@' + post.user.username"></p>
                        </div>
                    </div>
                    <p class="text-white text-sm mb-2" x-text="post.title"></p>
                    <p class="text-gray-100 text-xs prose prose-sm prose-invert" x-html="post.content"></p>

                    <!-- Seção de comentários (visível apenas quando showComments é true) -->
                    <div
                        x-show="post.showComments"
                        class="mt-4 bg-black bg-opacity-50 rounded-lg p-4 pointer-events-auto"
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                    >
                        <!-- Formulário de comentário -->
                        <form
                            x-data="{ commentText: '', isSubmitting: false }"
                            @submit.prevent="
                                if (commentText.trim() === '') return;
                                isSubmitting = true;

                                // Simular adição imediata do comentário para feedback visual
                                const tempComment = {
                                    id: 'temp-' + Date.now(),
                                    body: commentText,
                                    created_at: 'Agora mesmo',
                                    user: {
                                        id: {{ auth()->id() ?? 0 }},
                                        name: '{{ auth()->user()->name ?? 'Usuário' }}',
                                        username: '{{ auth()->user()->username ?? 'usuario' }}',
                                        avatar: '{{ auth()->user()->userPhotos->first() ? Storage::url(auth()->user()->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}'
                                    }
                                };

                                // Adicionar temporariamente à lista de comentários
                                post.comments.unshift(tempComment);
                                post.comments_count++;

                                // Enviar para o servidor
                                $wire.addComment(post.id, commentText)
                                    .then(() => {
                                        commentText = '';
                                        isSubmitting = false;
                                    })
                                    .catch(error => {
                                        console.error('Erro ao adicionar comentário:', error);
                                        // Remover o comentário temporário em caso de erro
                                        post.comments = post.comments.filter(c => c.id !== tempComment.id);
                                        post.comments_count--;
                                        isSubmitting = false;
                                    });
                            "
                            class="flex gap-2 mb-4 relative"
                            x-on:update-comment-text-{{ '${post.id}' }}.window="commentText = $event.detail.text"
                        >
                            <div x-data="mentionHashtagHandler()" class="flex-grow relative">
                                <input
                                    x-model="commentText"
                                    x-ref="textarea"
                                    x-on:input="handleInput($event)"
                                    x-on:keydown="handleKeydown($event)"
                                    @keyup="$wire.handleCommentInput(post.id, commentText)"
                                    @focus="$wire.setActiveCommentPostId(post.id); $wire.closeSuggestions()"
                                    type="text"
                                    class="mention-hashtag-input flex-1 p-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white"
                                    placeholder="Escreva um comentário..."
                                    :disabled="isSubmitting"
                                    autocomplete="off"
                                >
                                <!-- Sugestões de menções -->
                                <div
                                    x-show="showMentions"
                                    x-transition
                                    :style="mentionStyle"
                                    class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto"
                                >
                                    <template x-for="user in mentionSuggestions" :key="user.id">
                                        <div
                                            @click="selectMention(user.username)"
                                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                                        >
                                            <img :src="user.avatar" :alt="user.username" class="w-6 h-6 rounded-full mr-2">
                                            <span x-text="user.name + ' (@' + user.username + ')'"></span>
                                        </div>
                                    </template>
                                </div>

                                <!-- Sugestões de hashtags -->
                                <div
                                    x-show="showHashtags"
                                    x-transition
                                    :style="hashtagStyle"
                                    class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto"
                                >
                                    <template x-for="hashtag in hashtagSuggestions" :key="hashtag.id">
                                        <div
                                            @click="selectHashtag(hashtag.name)"
                                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                                        >
                                            <span x-text="'#' + hashtag.name"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            <button
                                type="submit"
                                class="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                :disabled="isSubmitting || commentText.trim() === ''"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </button>
                        </form>

                        <!-- Lista de comentários -->
                        <div class="space-y-3 max-h-60 overflow-y-auto">
                            <template x-for="(comment, commentIndex) in post.comments" :key="comment.id">
                                <div
                                    class="flex items-start space-x-3 p-3 bg-zinc-800 rounded-lg"
                                    x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 transform -translate-y-4"
                                    x-transition:enter-end="opacity-100 transform translate-y-0"
                                    :class="{'border-l-2 border-blue-500': comment.id.toString().includes('temp-')}"
                                >
                                    <img :src="comment.user.avatar" class="w-8 h-8 rounded-full object-cover">
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between">
                                            <p class="font-semibold text-gray-300" x-text="comment.user.username"></p>
                                            <div class="flex items-center space-x-2">
                                                <p class="text-gray-500 text-xs" x-text="comment.created_at"></p>
                                                <!-- Menu de opções do comentário -->
                                                <div x-data="{ showOptions: false, isEditing: false, editedText: '' }" class="relative">
                                                    <button
                                                        @click="showOptions = !showOptions"
                                                        class="text-gray-400 hover:text-white"
                                                        x-show="!isEditing"
                                                    >
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                                        </svg>
                                                    </button>
                                                    <!-- Menu dropdown -->
                                                    <div
                                                        x-show="showOptions"
                                                        @click.away="showOptions = false"
                                                        class="absolute right-0 mt-2 w-48 bg-zinc-900 rounded-md shadow-lg z-50"
                                                    >
                                                        <!-- Opção de editar -->
                                                        <button
                                                            x-show="comment.user.id === {{ auth()->id() ?? 0 }}"
                                                            @click="
                                                                showOptions = false;
                                                                isEditing = true;
                                                                editedText = comment.body.replace(/<[^>]*>/g, '');
                                                            "
                                                            class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-zinc-800"
                                                        >
                                                            Editar
                                                        </button>
                                                        <!-- Opção de deletar -->
                                                        <button
                                                            x-show="comment.user.id === {{ auth()->id() ?? 0 }}"
                                                            @click="
                                                                showOptions = false;
                                                                if (confirm('Tem certeza que deseja excluir este comentário?')) {
                                                                    $wire.deleteComment(post.id, comment.id);
                                                                    post.comments = post.comments.filter(c => c.id !== comment.id);
                                                                    post.comments_count--;
                                                                }
                                                            "
                                                            class="w-full text-left px-4 py-2 text-sm text-red-500 hover:bg-zinc-800"
                                                        >
                                                            Excluir
                                                        </button>
                                                    </div>
                                                    <!-- Formulário de edição -->
                                                    <div x-show="isEditing" class="editing-comment">
                                                        <div class="flex gap-2">
                                                            <input
                                                                x-model="editedText"
                                                                type="text"
                                                                class="flex-1 p-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
                                                                @keydown.enter.prevent="
                                                                    if (editedText.trim() !== '') {
                                                                        $wire.editComment(post.id, comment.id, editedText);
                                                                        comment.body = editedText;
                                                                        isEditing = false;
                                                                    }
                                                                "
                                                                @keydown.escape="isEditing = false"
                                                            >
                                                            <button
                                                                @click="
                                                                    if (editedText.trim() !== '') {
                                                                        $wire.editComment(post.id, comment.id, editedText);
                                                                        comment.body = editedText;
                                                                        isEditing = false;
                                                                    }
                                                                "
                                                                class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                                            >
                                                                Salvar
                                                            </button>
                                                            <button
                                                                @click="isEditing = false"
                                                                class="px-3 py-2 bg-zinc-700 text-white rounded hover:bg-zinc-600"
                                                            >
                                                                Cancelar
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-gray-100 prose prose-xs prose-invert" x-html="comment.body"></p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Botões de ação -->
                <div class="absolute bottom-20 right-4 flex flex-col items-center space-y-6 pointer-events-auto">
                    <!-- Botão de like -->
                    <div>
                        <button
                            @click.stop="
                                // Atualizar imediatamente para feedback visual instantâneo
                                post.liked_by_user = !post.liked_by_user;
                                post.likes_count += post.liked_by_user ? 1 : -1;
                                // Chamar o método do Livewire para persistir a mudança
                                $wire.likePost(post.id);
                            "
                            class="flex flex-col items-center"
                        >
                            <div class="relative">
                                <svg
                                    class="w-8 h-8 text-white"
                                    x-show="!post.liked_by_user"
                                    x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 scale-90"
                                    x-transition:enter-end="opacity-100 scale-100"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                <svg
                                    class="w-8 h-8 text-red-500 fill-current"
                                    x-show="post.liked_by_user"
                                    x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 scale-90"
                                    x-transition:enter-end="opacity-100 scale-100"
                                    fill="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="flex flex-col items-center">
                                <span class="text-white text-xs mt-1" x-text="post.likes_count"></span>
                                <span class="text-white text-xs" x-text="post.liked_by_user ? 'Curtido' : 'Curtir'"></span>
                            </div>
                        </button>
                    </div>

                    <!-- Botão de comentários -->
                    <div>
                        <button
                            @click.stop="
                                post.showComments = !post.showComments;
                                if (post.showComments) {
                                    // Focar no campo de comentário quando abrir
                                    setTimeout(() => {
                                        const input = document.querySelector(`#story-${post.id} form input`);
                                        if (input) input.focus();
                                    }, 300);
                                }
                            "
                            class="flex flex-col items-center"
                        >
                            <div class="relative">
                                <svg
                                    class="w-8 h-8 text-white"
                                    x-show="!post.showComments"
                                    x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 scale-90"
                                    x-transition:enter-end="opacity-100 scale-100"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <svg
                                    class="w-8 h-8 text-blue-500 fill-current"
                                    x-show="post.showComments"
                                    x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 scale-90"
                                    x-transition:enter-end="opacity-100 scale-100"
                                    fill="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <div class="flex flex-col items-center">
                                <span class="text-white text-xs mt-1" x-text="post.comments_count"></span>
                                <span class="text-white text-xs" x-text="post.showComments ? 'Comentando' : 'Comentar'"></span>
                            </div>
                        </button>
                    </div>

                    <!-- Botão de compartilhar -->
                    <div>
                        <div x-data="{ showShareMenu: false }" class="relative">
                            <button
                                @click="showShareMenu = !showShareMenu"
                                class="flex flex-col items-center"
                            >
                                <svg
                                    class="w-8 h-8 text-white"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                <span class="text-white text-xs mt-1">Compartilhar</span>
                            </button>
                            <!-- Menu de compartilhamento -->
                            <div
                                x-show="showShareMenu"
                                @click.away="showShareMenu = false"
                                class="absolute bottom-full right-0 mb-2 w-48 bg-zinc-900 rounded-md shadow-lg z-50"
                            >
                                <!-- Copiar link -->
                                <button
                                    @click="
                                        showShareMenu = false;
                                        const url = window.location.origin + '/feed_imagens?post=' + post.id;
                                        navigator.clipboard.writeText(url).then(() => {
                                            alert('Link copiado para a área de transferência!');
                                        });
                                    "
                                    class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-zinc-800 flex items-center"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                    Copiar link
                                </button>
                                <!-- Compartilhar no WhatsApp -->
                                <a
                                    :href="'https://wa.me/?text=' + encodeURIComponent('Confira esta imagem: ' + window.location.origin + '/feed_imagens?post=' + post.id)"
                                    target="_blank"
                                    @click="showShareMenu = false"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-zinc-800 flex items-center"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    WhatsApp
                                </a>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Navigation Indicators -->
                <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-2 z-10">
                    <template x-for="(_, i) in posts" :key="i">
                        <div 
                            class="w-1 h-8 rounded-full transition-all duration-300"
                            :class="i === currentIndex ? 'bg-white' : 'bg-white/30'"
                        ></div>
                    </template>
                </div>
            </div>
        </template>
    </div>

    <!-- Loading State -->
    <div x-show="posts.length === 0" class="h-screen w-full flex items-center justify-center bg-black">
        <div class="text-center">
            <svg class="animate-spin h-8 w-8 text-white mx-auto mb-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="text-white/70">Carregando imagens...</p>
        </div>
    </div>
</div>
