/* Ranking Avatar Borders */

/* Base avatar container */
.ranking-avatar {
    position: relative;
    display: inline-block;
    z-index: 10;
}

/* Bronze Border (Positions 11-20) */
.ranking-avatar.bronze::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, #CD7F32, #B8860B, #CD7F32);
    z-index: -1;
    animation: bronzeGlow 3s ease-in-out infinite alternate;
}

.ranking-avatar.bronze::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #CD7F32;
    z-index: 1;
}

/* Silver Border (Positions 4-10) */
.ranking-avatar.silver::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, #C0C0C0, #E5E5E5, #C0C0C0);
    z-index: -1;
    animation: silverGlow 2.5s ease-in-out infinite alternate;
}

.ranking-avatar.silver::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #C0C0C0;
    z-index: 1;
}

/* Gold Border (Position 3) */
.ranking-avatar.gold::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700);
    z-index: -1;
    animation: goldGlow 2s ease-in-out infinite alternate;
}

.ranking-avatar.gold::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #FFD700;
    z-index: 1;
}

/* Diamond Border (Position 2) */
.ranking-avatar.diamond::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #B9F2FF, #00FFFF, #B9F2FF);
    z-index: -1;
    animation: diamondGlow 1.5s ease-in-out infinite alternate;
}

.ranking-avatar.diamond::after {
    content: '💎';
    position: absolute;
    bottom: -14px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    z-index: 1;
    animation: bounce 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 255, 255, 0.5));
}

/* Crown Border (Position 1) */
.ranking-avatar.crown::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FFD700, #FF6B35, #FFD700, #FF1493, #FFD700);
    background-size: 300% 300%;
    z-index: -1;
    animation: crownGlow 1s ease-in-out infinite alternate;
}

.ranking-avatar.crown::after {
    content: '👑';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    z-index: 1;
    animation: crownFloat 3s ease-in-out infinite;
}

/* Wings removed - no longer used */

/* Animations */
@keyframes bronzeGlow {
    0% {
        box-shadow: 0 0 5px rgba(205, 127, 50, 0.5);
    }

    100% {
        box-shadow: 0 0 15px rgba(205, 127, 50, 0.8);
    }
}

@keyframes silverGlow {
    0% {
        box-shadow: 0 0 8px rgba(192, 192, 192, 0.5);
    }

    100% {
        box-shadow: 0 0 20px rgba(192, 192, 192, 0.8);
    }
}

@keyframes goldGlow {
    0% {
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }

    100% {
        box-shadow: 0 0 25px rgba(255, 215, 0, 0.9);
    }
}

@keyframes diamondGlow {
    0% {
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    }

    100% {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.9);
    }
}

@keyframes crownGlow {
    0% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.7);
        background-position: 0% 50%;
    }

    100% {
        box-shadow: 0 0 40px rgba(255, 107, 53, 0.9);
        background-position: 100% 50%;
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-5px);
    }

    60% {
        transform: translateX(-50%) translateY(-3px);
    }
}

@keyframes crownFloat {

    0%,
    100% {
        transform: translateX(-50%) translateY(0px);
    }

    50% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* wingFlap animation removed */

/* Responsive adjustments */
@media (max-width: 768px) {
    .ranking-avatar.crown::after {
        font-size: 12px;
        top: -16px;
    }

    .ranking-avatar.diamond::after {
        font-size: 14px;
        bottom: -12px;
    }

    /* Wings removed */

    /* Bordas mais finas em mobile */
    .ranking-avatar.bronze::before {
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
    }

    .ranking-avatar.silver::before {
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
    }

    .ranking-avatar.gold::before {
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
    }

    .ranking-avatar.diamond::before {
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
    }

    .ranking-avatar.crown::before {
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
    }
}