<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Configurações de notificação
            $table->boolean('notification_sound')->default(true);
            $table->boolean('browser_notifications')->default(false);
            $table->boolean('notification_vibration')->default(true);
            $table->boolean('do_not_disturb')->default(false);
            $table->time('dnd_start_time')->nullable();
            $table->time('dnd_end_time')->nullable();
            $table->decimal('notification_volume', 3, 2)->default(0.5);
            $table->boolean('notification_preview')->default(true);
            $table->boolean('group_notifications')->default(true);
            $table->integer('max_notifications')->default(5);

            // Tipos de notificação
            $table->boolean('notify_messages')->default(true);
            $table->boolean('notify_mentions')->default(true);
            $table->boolean('notify_likes')->default(true);
            $table->boolean('notify_follows')->default(true);
            $table->boolean('notify_system')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'notification_sound',
                'browser_notifications',
                'notification_vibration',
                'do_not_disturb',
                'dnd_start_time',
                'dnd_end_time',
                'notification_volume',
                'notification_preview',
                'group_notifications',
                'max_notifications',
                'notify_messages',
                'notify_mentions',
                'notify_likes',
                'notify_follows',
                'notify_system'
            ]);
        });
    }
};
