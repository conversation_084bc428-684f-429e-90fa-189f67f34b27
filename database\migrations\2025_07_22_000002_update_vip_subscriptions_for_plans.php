<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vip_subscriptions', function (Blueprint $table) {
            // Adicionar referência ao plano
            $table->foreignId('plan_id')->nullable()->after('user_id')->constrained('subscription_plans')->onDelete('set null');
            
            // Adicionar campos para integração com Stripe Subscriptions
            $table->string('stripe_subscription_id')->nullable()->after('stripe_payment_id');
            $table->string('stripe_customer_id')->nullable()->after('stripe_subscription_id');
            
            // Adicionar campos para controle de trial
            $table->timestamp('trial_ends_at')->nullable()->after('expires_at');
            
            // Adicionar campos para controle de cancelamento
            $table->timestamp('canceled_at')->nullable()->after('trial_ends_at');
            $table->timestamp('ends_at')->nullable()->after('canceled_at'); // Quando a assinatura realmente termina
            
            // Adicionar índices
            $table->index('stripe_subscription_id');
            $table->index('stripe_customer_id');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vip_subscriptions', function (Blueprint $table) {
            $table->dropForeign(['plan_id']);
            $table->dropColumn([
                'plan_id',
                'stripe_subscription_id',
                'stripe_customer_id',
                'trial_ends_at',
                'canceled_at',
                'ends_at'
            ]);
        });
    }
};
