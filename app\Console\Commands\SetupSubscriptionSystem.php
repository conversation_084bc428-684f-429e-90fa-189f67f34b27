<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupSubscriptionSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'subscription:setup {--force : Force setup even if already configured}';

    /**
     * The console command description.
     */
    protected $description = 'Setup the complete subscription system with plans, migrations and Stripe products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up subscription system...');
        $this->newLine();

        // Step 1: Run migrations
        $this->info('📦 Running migrations...');
        try {
            Artisan::call('migrate', ['--force' => true]);
            $this->line('✅ Migrations completed successfully');
        } catch (\Exception $e) {
            $this->error('❌ Migration failed: ' . $e->getMessage());
            return 1;
        }
        $this->newLine();

        // Step 2: Seed subscription plans
        $this->info('🌱 Seeding subscription plans...');
        try {
            Artisan::call('db:seed', ['--class' => 'SubscriptionPlanSeeder', '--force' => true]);
            $this->line('✅ Subscription plans seeded successfully');
        } catch (\Exception $e) {
            $this->error('❌ Seeding failed: ' . $e->getMessage());
            return 1;
        }
        $this->newLine();

        // Step 3: Setup Stripe products (if API key is configured)
        if (config('cashier.secret')) {
            $this->info('💳 Setting up Stripe products and prices...');
            try {
                $forceFlag = $this->option('force') ? ['--force' => true] : [];
                Artisan::call('stripe:setup-products', $forceFlag);
                $this->line('✅ Stripe products configured successfully');
            } catch (\Exception $e) {
                $this->error('❌ Stripe setup failed: ' . $e->getMessage());
                $this->warn('💡 Make sure your Stripe API keys are configured in .env');
                return 1;
            }
        } else {
            $this->warn('⚠️  Stripe API key not configured. Skipping Stripe setup.');
            $this->line('💡 Configure STRIPE_SECRET in your .env file and run: php artisan stripe:setup-products');
        }
        $this->newLine();

        // Step 4: Display next steps
        $this->displayNextSteps();

        $this->info('🎉 Subscription system setup completed!');
        return 0;
    }

    /**
     * Display next steps for the user
     */
    private function displayNextSteps()
    {
        $this->info('📋 Next Steps:');
        $this->line('');
        
        $this->line('1. 🔧 Configure your Stripe webhook endpoint:');
        $this->line('   URL: ' . url('/stripe/webhook'));
        $this->line('   Events: customer.subscription.created, customer.subscription.updated,');
        $this->line('           customer.subscription.deleted, invoice.payment_succeeded,');
        $this->line('           invoice.payment_failed, checkout.session.completed');
        $this->line('');
        
        $this->line('2. 🔑 Set your Stripe webhook secret in .env:');
        $this->line('   STRIPE_WEBHOOK_SECRET=whsec_...');
        $this->line('');
        
        $this->line('3. 🎨 The subscription plans page is available at:');
        $this->line('   ' . route('renovar-vip'));
        $this->line('');
        
        $this->line('4. 🧪 Test the subscription flow:');
        $this->line('   - Visit the plans page');
        $this->line('   - Select a plan and complete checkout');
        $this->line('   - Verify webhooks are working');
        $this->line('');
        
        $this->line('5. 📊 Monitor subscriptions in:');
        $this->line('   - Stripe Dashboard');
        $this->line('   - Application logs');
        $this->line('   - Database tables: subscription_plans, vip_subscriptions');
        $this->line('');
    }
}
