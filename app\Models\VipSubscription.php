<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class VipSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'plan_days',
        'amount',
        'status',
        'stripe_session_id',
        'stripe_payment_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'activated_at',
        'expires_at',
        'trial_ends_at',
        'canceled_at',
        'ends_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'plan_days' => 'integer',
        'activated_at' => 'datetime',
        'expires_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'canceled_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    /**
     * Status possíveis para assinaturas
     */
    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELED = 'canceled';
    const STATUS_EXPIRED = 'expired';
    const STATUS_TRIALING = 'trialing';
    const STATUS_PAST_DUE = 'past_due';
    const STATUS_UNPAID = 'unpaid';

    /**
     * Get the user that owns the subscription
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Check if the subscription is active
     */
    public function isActive()
    {
        return in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_TRIALING])
            && (!$this->ends_at || $this->ends_at > Carbon::now());
    }

    /**
     * Check if the subscription has expired
     */
    public function hasExpired()
    {
        return $this->status === self::STATUS_EXPIRED
            || ($this->ends_at && $this->ends_at <= Carbon::now());
    }

    /**
     * Check if the subscription is on trial
     */
    public function onTrial()
    {
        return $this->status === self::STATUS_TRIALING
            && $this->trial_ends_at
            && $this->trial_ends_at > Carbon::now();
    }

    /**
     * Check if the subscription is canceled
     */
    public function isCanceled()
    {
        return $this->status === self::STATUS_CANCELED;
    }

    /**
     * Check if the subscription is past due
     */
    public function isPastDue()
    {
        return $this->status === self::STATUS_PAST_DUE;
    }

    /**
     * Get the remaining days of the subscription
     */
    public function getRemainingDays()
    {
        if (!$this->isActive()) {
            return 0;
        }

        $endDate = $this->ends_at ?? $this->expires_at;
        if (!$endDate) {
            return 0;
        }

        return max(0, Carbon::now()->diffInDays($endDate, false));
    }

    /**
     * Get the remaining trial days
     */
    public function getRemainingTrialDays()
    {
        if (!$this->onTrial()) {
            return 0;
        }

        return max(0, Carbon::now()->diffInDays($this->trial_ends_at, false));
    }

    /**
     * Scope para assinaturas ativas
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [self::STATUS_ACTIVE, self::STATUS_TRIALING])
            ->where(function ($q) {
                $q->whereNull('ends_at')
                  ->orWhere('ends_at', '>', Carbon::now());
            });
    }

    /**
     * Scope para assinaturas expiradas
     */
    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_EXPIRED)
            ->orWhere(function ($q) {
                $q->whereNotNull('ends_at')
                  ->where('ends_at', '<=', Carbon::now());
            });
    }

    /**
     * Scope para assinaturas canceladas
     */
    public function scopeCanceled($query)
    {
        return $query->where('status', self::STATUS_CANCELED);
    }
}
