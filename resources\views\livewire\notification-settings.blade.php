<div class="max-w-4xl mx-auto p-6 space-y-8">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-title">Configurações de Notificação</h1>
            <p class="text-body-lighter mt-1">Personalize como você recebe notificações</p>
        </div>
        
        <div class="flex gap-2">
            <flux:button wire:click="resetToDefaults" variant="outline" size="sm">
                Restaurar Padrões
            </flux:button>
            <flux:button wire:click="saveSettings" variant="primary" size="sm">
                Salvar Configurações
            </flux:button>
        </div>
    </div>

    <!-- Configurações Gerais -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm border border-zinc-200 dark:border-zinc-700">
        <h2 class="text-lg font-semibold text-title mb-4">Configurações Gerais</h2>
        
        <div class="space-y-6">
            <!-- Som -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Sons de Notificação</label>
                    <p class="text-xs text-body-lighter">Reproduzir sons quando receber notificações</p>
                </div>
                <div class="flex items-center gap-2">
                    <flux:button 
                        wire:click="testNotification('message')" 
                        variant="ghost" 
                        size="sm"
                        icon="speaker-wave">
                        Testar
                    </flux:button>
                    <flux:switch wire:model.live="soundEnabled" />
                </div>
            </div>

            <!-- Volume -->
            @if($soundEnabled)
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Volume</label>
                    <p class="text-xs text-body-lighter">Ajustar volume dos sons</p>
                </div>
                <div class="w-32">
                    <input 
                        type="range" 
                        min="0" 
                        max="1" 
                        step="0.1" 
                        wire:model.live="soundVolume"
                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    >
                    <div class="text-xs text-center text-body-lighter mt-1">{{ round($soundVolume * 100) }}%</div>
                </div>
            </div>
            @endif

            <!-- Notificações do Browser -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Notificações do Navegador</label>
                    <p class="text-xs text-body-lighter">Mostrar notificações mesmo quando a aba não estiver ativa</p>
                </div>
                <div class="flex items-center gap-2">
                    @if(!$browserNotificationsEnabled)
                    <flux:button 
                        wire:click="requestBrowserPermission" 
                        variant="ghost" 
                        size="sm"
                        icon="bell">
                        Permitir
                    </flux:button>
                    @endif
                    <flux:switch wire:model.live="browserNotificationsEnabled" />
                </div>
            </div>

            <!-- Vibração -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Vibração</label>
                    <p class="text-xs text-body-lighter">Vibrar dispositivo em notificações (apenas mobile)</p>
                </div>
                <flux:switch wire:model.live="vibrationEnabled" />
            </div>

            <!-- Prévia de Mensagens -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Prévia de Mensagens</label>
                    <p class="text-xs text-body-lighter">Mostrar conteúdo das mensagens nas notificações</p>
                </div>
                <flux:switch wire:model.live="showPreview" />
            </div>

            <!-- Agrupar Notificações -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Agrupar Notificações</label>
                    <p class="text-xs text-body-lighter">Agrupar múltiplas notificações do mesmo tipo</p>
                </div>
                <flux:switch wire:model.live="groupNotifications" />
            </div>

            <!-- Máximo de Notificações -->
            <div class="flex items-center justify-between">
                <div>
                    <label class="text-sm font-medium text-title">Máximo de Notificações</label>
                    <p class="text-xs text-body-lighter">Número máximo de notificações visíveis</p>
                </div>
                <div class="w-20">
                    <flux:input 
                        type="number" 
                        min="1" 
                        max="10" 
                        wire:model.live="maxNotifications"
                        size="sm"
                    />
                </div>
            </div>
        </div>
    </div>

    <!-- Modo Não Perturbe -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm border border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h2 class="text-lg font-semibold text-title">Modo Não Perturbe</h2>
                <p class="text-sm text-body-lighter">Silenciar notificações em horários específicos</p>
            </div>
            <flux:switch wire:model.live="doNotDisturbMode" />
        </div>

        @if($doNotDisturbMode)
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-title mb-2">Início</label>
                <flux:input 
                    type="time" 
                    wire:model.live="doNotDisturbStart"
                />
            </div>
            <div>
                <label class="block text-sm font-medium text-title mb-2">Fim</label>
                <flux:input 
                    type="time" 
                    wire:model.live="doNotDisturbEnd"
                />
            </div>
        </div>
        <p class="text-xs text-body-lighter mt-2">
            Durante este período, apenas notificações de alta prioridade serão exibidas
        </p>
        @endif
    </div>

    <!-- Tipos de Notificação -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm border border-zinc-200 dark:border-zinc-700">
        <h2 class="text-lg font-semibold text-title mb-4">Tipos de Notificação</h2>
        
        <div class="space-y-4">
            @foreach($this->notificationTypes as $type => $config)
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <x-flux::icon name="{{ $config['icon'] }}" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                        <h3 class="font-medium text-title">{{ $config['label'] }}</h3>
                        <p class="text-xs text-body-lighter">{{ $config['description'] }}</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <flux:button 
                        wire:click="testNotification('{{ $type }}')" 
                        variant="ghost" 
                        size="sm"
                        icon="play">
                        Testar
                    </flux:button>
                    <flux:switch wire:model.live="{{ $type }}Notifications" />
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm border border-zinc-200 dark:border-zinc-700">
        <h2 class="text-lg font-semibold text-title mb-4">Ações Rápidas</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <flux:button 
                wire:click="testNotification('message')" 
                variant="outline" 
                class="w-full"
                icon="chat-bubble-left-right">
                Testar Mensagem
            </flux:button>
            
            <flux:button 
                wire:click="testNotification('mention')" 
                variant="outline" 
                class="w-full"
                icon="at-symbol">
                Testar Menção
            </flux:button>
            
            <flux:button 
                wire:click="toggleDoNotDisturb" 
                variant="outline" 
                class="w-full"
                icon="moon">
                {{ $doNotDisturbMode ? 'Desativar' : 'Ativar' }} Não Perturbe
            </flux:button>
        </div>
    </div>
</div>

@push('scripts')
@vite('resources/js/notification-config.js')
<script>
    // Escutar eventos de configuração
    Livewire.on('notification-settings-updated', (settings) => {
        if (window.notificationSettings) {
            window.notificationSettings.settings = { ...window.notificationSettings.settings, ...settings };
            window.notificationSettings.saveSettings();
        }
    });

    // Escutar solicitação de permissão do browser
    Livewire.on('request-browser-permission', () => {
        if ('Notification' in window) {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    @this.set('browserNotificationsEnabled', true);
                    @this.saveSettings();
                }
            });
        }
    });

    // Escutar testes de notificação
    Livewire.on('test-notification', (data) => {
        // Mostrar toast
        window.dispatchEvent(new CustomEvent('notify', {
            detail: data
        }));

        // Testar notificação do browser se habilitada
        if (window.notificationSettings?.settings.browserNotificationsEnabled && 'Notification' in window && Notification.permission === 'granted') {
            new Notification(`Teste: ${data.message}`, {
                icon: data.avatar,
                tag: 'test-notification'
            });
        }

        // Testar vibração se habilitada
        if (window.notificationSettings?.settings.vibrationEnabled && 'vibrate' in navigator) {
            navigator.vibrate([200, 100, 200]);
        }
    });
</script>
@endpush
