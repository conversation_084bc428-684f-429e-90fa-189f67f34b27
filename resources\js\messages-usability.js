/**
 * Melhorias de Usabilidade para Sistema de Mensagens
 * Inclui atalhos de teclado, gestos, indicadores de digitação e notificações avançadas
 */

// Sistema de Atalhos de Teclado
class MessageKeyboardShortcuts {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        console.log('🎹 Sistema de atalhos de teclado inicializado');
    }

    handleKeydown(event) {
        // Verificar se estamos em um input de mensagem
        const isMessageInput = event.target.classList.contains('message-input');

        // Atalhos globais (funcionam em qualquer lugar)
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'k':
                case 'K':
                    event.preventDefault();
                    this.focusSearch();
                    break;
                case 'm':
                case 'M':
                    event.preventDefault();
                    this.goToMessages();
                    break;
                case '/':
                    event.preventDefault();
                    this.focusMessageInput();
                    break;
            }
        }

        // Atalhos específicos para input de mensagem
        if (isMessageInput) {
            switch (event.key) {
                case 'Enter':
                    if (event.shiftKey) {
                        // Shift+Enter = nova linha (comportamento padrão)
                        return;
                    } else {
                        // Enter = enviar mensagem
                        event.preventDefault();
                        this.sendMessage();
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.clearMessageInput();
                    break;
                case 'ArrowUp':
                    if (event.target.value === '') {
                        event.preventDefault();
                        this.editLastMessage();
                    }
                    break;
            }
        }

        // Navegação entre conversas
        if (!isMessageInput && !event.ctrlKey && !event.metaKey) {
            switch (event.key) {
                case 'ArrowUp':
                case 'k':
                    event.preventDefault();
                    this.selectPreviousConversation();
                    break;
                case 'ArrowDown':
                case 'j':
                    event.preventDefault();
                    this.selectNextConversation();
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.openSelectedConversation();
                    break;
            }
        }
    }

    focusSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    goToMessages() {
        window.location.href = '/messages';
    }

    focusMessageInput() {
        const messageInput = document.querySelector('.message-input');
        if (messageInput) {
            messageInput.focus();
        }
    }

    sendMessage() {
        const form = document.querySelector('form[wire\\:submit\\.prevent="sendMessage"]');
        if (form) {
            form.dispatchEvent(new Event('submit'));

            // Forçar scroll para baixo após enviar mensagem
            setTimeout(() => {
                if (window.autoScrollInstance) {
                    window.autoScrollInstance.forceScrollToBottom();
                }
            }, 100);
        }
    }

    clearMessageInput() {
        const messageInput = document.querySelector('.message-input');
        if (messageInput && window.Livewire) {
            // Limpar via Livewire
            window.Livewire.find(messageInput.closest('[wire\\:id]')).set('messageBody', '');
        }
    }

    editLastMessage() {
        // Implementar edição da última mensagem
        console.log('🔄 Editando última mensagem...');
    }

    selectPreviousConversation() {
        this.navigateConversations(-1);
    }

    selectNextConversation() {
        this.navigateConversations(1);
    }

    navigateConversations(direction) {
        const conversations = document.querySelectorAll('.conversation-item');
        const current = document.querySelector('.conversation-item.active') ||
            document.querySelector('.conversation-item[class*="bg-purple"]');

        if (conversations.length === 0) return;

        let currentIndex = Array.from(conversations).indexOf(current);
        if (currentIndex === -1) currentIndex = 0;

        const nextIndex = Math.max(0, Math.min(conversations.length - 1, currentIndex + direction));
        const nextConversation = conversations[nextIndex];

        if (nextConversation) {
            nextConversation.click();
            nextConversation.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    openSelectedConversation() {
        const selected = document.querySelector('.conversation-item.active') ||
            document.querySelector('.conversation-item[class*="bg-purple"]');
        if (selected) {
            selected.click();
        }
    }
}

// Sistema de Indicadores de Digitação
class TypingIndicator {
    constructor() {
        this.typingUsers = new Set();
        this.typingTimeout = null;
        this.init();
    }

    init() {
        // Detectar quando o usuário está digitando
        const messageInput = document.querySelector('.message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => this.handleTyping());
            messageInput.addEventListener('blur', () => this.stopTyping());
        }

        // Escutar eventos de digitação de outros usuários
        if (window.Livewire) {
            Livewire.on('user-typing', (data) => this.showTypingIndicator(data));
            Livewire.on('user-stopped-typing', (data) => this.hideTypingIndicator(data));
        }

        console.log('⌨️ Sistema de indicadores de digitação inicializado');
    }

    handleTyping() {
        // Enviar evento de que o usuário está digitando
        if (window.Livewire) {
            const component = Livewire.find(document.querySelector('[wire\\:id]'));
            if (component) {
                component.call('userTyping');
            }
        }

        // Resetar timeout para parar de digitar
        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => this.stopTyping(), 3000);
    }

    stopTyping() {
        if (window.Livewire) {
            const component = Livewire.find(document.querySelector('[wire\\:id]'));
            if (component) {
                component.call('userStoppedTyping');
            }
        }
        clearTimeout(this.typingTimeout);
    }

    showTypingIndicator(data) {
        this.typingUsers.add(data.user_id);
        this.updateTypingDisplay();
    }

    hideTypingIndicator(data) {
        this.typingUsers.delete(data.user_id);
        this.updateTypingDisplay();
    }

    updateTypingDisplay() {
        const container = document.querySelector('.typing-indicator-container');
        if (!container) {
            this.createTypingIndicatorContainer();
            return;
        }

        if (this.typingUsers.size > 0) {
            container.style.display = 'block';
            container.innerHTML = this.getTypingText();
        } else {
            container.style.display = 'none';
        }
    }

    createTypingIndicatorContainer() {
        const messagesContainer = document.querySelector('#message-container');
        if (!messagesContainer) return;

        const container = document.createElement('div');
        container.className = 'typing-indicator-container p-2 text-sm text-gray-500 dark:text-gray-400';
        container.style.display = 'none';

        messagesContainer.appendChild(container);
    }

    getTypingText() {
        const count = this.typingUsers.size;
        if (count === 1) {
            return '<div class="flex items-center gap-2"><div class="typing-dots"></div>Alguém está digitando...</div>';
        } else {
            return `<div class="flex items-center gap-2"><div class="typing-dots"></div>${count} pessoas estão digitando...</div>`;
        }
    }
}

// Sistema de Notificações Avançadas
class AdvancedNotifications {
    constructor() {
        this.permission = 'default';
        this.sounds = {
            message: '/sounds/message.mp3',
            mention: '/sounds/mention.mp3',
            notification: '/sounds/notification.mp3'
        };
        this.init();
    }

    async init() {
        // Solicitar permissão para notificações
        if ('Notification' in window) {
            this.permission = await Notification.requestPermission();
        }

        // Configurar sons
        this.preloadSounds();

        // Escutar eventos de notificação
        if (window.Livewire) {
            Livewire.on('new-message-notification', (data) => this.showMessageNotification(data));
            Livewire.on('mention-notification', (data) => this.showMentionNotification(data));
        }

        console.log('🔔 Sistema de notificações avançadas inicializado');
    }

    preloadSounds() {
        Object.values(this.sounds).forEach(soundPath => {
            const audio = new Audio(soundPath);
            audio.preload = 'auto';
        });
    }

    async showMessageNotification(data) {
        // Verificar se a página está em foco
        if (document.hasFocus()) return;

        // Mostrar notificação do browser
        if (this.permission === 'granted') {
            const notification = new Notification(`Nova mensagem de ${data.sender_name}`, {
                body: data.message_preview,
                icon: data.avatar || '/images/users/avatar.svg',
                tag: `message-${data.conversation_id}`,
                requireInteraction: false,
                silent: false
            });

            notification.onclick = () => {
                window.focus();
                window.location.href = `/messages?conversation=${data.conversation_id}`;
                notification.close();
            };

            // Auto-fechar após 5 segundos
            setTimeout(() => notification.close(), 5000);
        }

        // Tocar som
        this.playSound('message');

        // Atualizar badge no título
        this.updatePageTitle(data.unread_count);
    }

    showMentionNotification(data) {
        if (this.permission === 'granted') {
            const notification = new Notification(`${data.sender_name} mencionou você`, {
                body: data.message_preview,
                icon: data.avatar || '/images/users/avatar.svg',
                tag: `mention-${data.message_id}`,
                requireInteraction: true
            });

            notification.onclick = () => {
                window.focus();
                window.location.href = data.url;
                notification.close();
            };
        }

        this.playSound('mention');
    }

    playSound(type) {
        if (this.sounds[type]) {
            const audio = new Audio(this.sounds[type]);
            audio.volume = 0.5;
            audio.play().catch(e => console.log('Não foi possível reproduzir o som:', e));
        }
    }

    updatePageTitle(unreadCount) {
        const originalTitle = document.title.replace(/^\(\d+\) /, '');
        document.title = unreadCount > 0 ? `(${unreadCount}) ${originalTitle}` : originalTitle;
    }
}

// Sistema de Gestos para Mobile
class MobileGestures {
    constructor() {
        this.startX = 0;
        this.startY = 0;
        this.threshold = 50;
        this.init();
    }

    init() {
        if (!this.isMobile()) return;

        document.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e));

        console.log('📱 Sistema de gestos mobile inicializado');
    }

    isMobile() {
        return window.innerWidth <= 768;
    }

    handleTouchStart(e) {
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
    }

    handleTouchEnd(e) {
        if (!this.startX || !this.startY) return;

        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;

        const diffX = this.startX - endX;
        const diffY = this.startY - endY;

        // Verificar se é um swipe horizontal
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > this.threshold) {
            if (diffX > 0) {
                this.handleSwipeLeft();
            } else {
                this.handleSwipeRight();
            }
        }

        this.startX = 0;
        this.startY = 0;
    }

    handleSwipeLeft() {
        // Swipe left: mostrar área de chat (esconder conversas)
        const conversationsSidebar = document.querySelector('.conversations-sidebar');
        const chatArea = document.querySelector('.chat-area');

        if (conversationsSidebar && chatArea) {
            conversationsSidebar.style.display = 'none';
            chatArea.style.width = '100%';
        }
    }

    handleSwipeRight() {
        // Swipe right: mostrar conversas (esconder chat)
        const conversationsSidebar = document.querySelector('.conversations-sidebar');
        const chatArea = document.querySelector('.chat-area');

        if (conversationsSidebar && chatArea) {
            conversationsSidebar.style.display = 'block';
            chatArea.style.display = 'none';
        }
    }
}

// Sistema de Scroll Automático
class AutoScroll {
    constructor() {
        this.container = null;
        this.isUserScrolling = false;
        this.scrollTimeout = null;
        this.init();
    }

    init() {
        this.container = document.getElementById('message-container');
        if (!this.container) return;

        // Detectar quando usuário está fazendo scroll manual
        this.container.addEventListener('scroll', () => this.handleUserScroll());

        // Observer para novas mensagens
        this.setupMessageObserver();

        // Escutar eventos Livewire
        this.setupLivewireListeners();

        // Scroll inicial
        setTimeout(() => this.scrollToBottom(false), 500);

        console.log('📜 Sistema de scroll automático inicializado');
    }

    handleUserScroll() {
        this.isUserScrolling = true;
        clearTimeout(this.scrollTimeout);

        // Resetar flag após 2 segundos de inatividade
        this.scrollTimeout = setTimeout(() => {
            this.isUserScrolling = false;
        }, 2000);
    }

    setupMessageObserver() {
        if (!this.container) return;

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Nova mensagem adicionada
                    this.handleNewMessage();
                }
            });
        });

        observer.observe(this.container, {
            childList: true,
            subtree: true
        });
    }

    setupLivewireListeners() {
        // Escutar quando mensagem é enviada
        if (window.Livewire) {
            Livewire.on('message-sent', () => {
                setTimeout(() => this.forceScrollToBottom(), 100);
            });

            Livewire.on('message-received', () => {
                this.handleNewMessage();
            });
        }
    }

    handleNewMessage() {
        // Só fazer scroll automático se usuário não estiver scrolling manualmente
        if (!this.isUserScrolling) {
            this.scrollToBottom(true);
        } else {
            // Mostrar indicador de nova mensagem
            this.showNewMessageIndicator();
        }
    }

    scrollToBottom(smooth = true) {
        if (!this.container) return;

        const scrollOptions = {
            top: this.container.scrollHeight,
            behavior: smooth ? 'smooth' : 'auto'
        };

        this.container.scrollTo(scrollOptions);
    }

    showNewMessageIndicator() {
        // Criar ou mostrar indicador de nova mensagem
        let indicator = document.querySelector('.new-message-indicator');

        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'new-message-indicator fixed bottom-20 right-4 bg-purple-500 text-white px-3 py-2 rounded-full shadow-lg cursor-pointer z-50 transition-all duration-300';
            indicator.innerHTML = '↓ Nova mensagem';
            indicator.onclick = () => {
                this.scrollToBottom(true);
                this.hideNewMessageIndicator();
            };
            document.body.appendChild(indicator);
        }

        indicator.style.opacity = '1';
        indicator.style.visibility = 'visible';
        indicator.style.transform = 'translateY(0)';
    }

    hideNewMessageIndicator() {
        const indicator = document.querySelector('.new-message-indicator');
        if (indicator) {
            indicator.style.opacity = '0';
            indicator.style.visibility = 'hidden';
            indicator.style.transform = 'translateY(10px)';
        }
    }

    // Método público para forçar scroll
    forceScrollToBottom() {
        this.isUserScrolling = false;
        this.scrollToBottom(true);
        this.hideNewMessageIndicator();
    }
}

// Sistema de Layout Mobile Responsivo
class MobileLayout {
    constructor() {
        this.isMobile = window.innerWidth <= 640;
        this.init();
    }

    init() {
        if (!this.isMobile) return;

        this.setupHorizontalConversations();
        this.setupMobileGestures();

        // Reconfigurar quando orientação mudar
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.setupHorizontalConversations(), 500);
        });

        console.log('📱 Layout mobile inicializado');
    }

    setupHorizontalConversations() {
        const sidebar = document.querySelector('.conversations-sidebar');
        const conversationsList = sidebar?.querySelector('.space-y-1');

        if (!sidebar || !conversationsList) return;

        // Adicionar classe para layout horizontal
        conversationsList.classList.remove('space-y-1');
        conversationsList.classList.add('conversations-list');

        // Configurar scroll horizontal suave
        sidebar.style.scrollBehavior = 'smooth';

        // Adicionar indicadores de scroll
        this.addScrollIndicators(sidebar);
    }

    addScrollIndicators(container) {
        // Verificar se precisa de indicadores
        const needsScroll = container.scrollWidth > container.clientWidth;

        if (needsScroll) {
            container.classList.add('has-scroll-indicators');
        }
    }

    setupMobileGestures() {
        // Gestos específicos para layout horizontal
        let startX = 0;

        const sidebar = document.querySelector('.conversations-sidebar');
        if (!sidebar) return;

        sidebar.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });

        sidebar.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const diff = startX - endX;

            // Swipe horizontal para navegar entre conversas
            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    // Swipe left - próxima conversa
                    this.navigateConversation(1);
                } else {
                    // Swipe right - conversa anterior
                    this.navigateConversation(-1);
                }
            }
        });
    }

    navigateConversation(direction) {
        const conversations = document.querySelectorAll('.conversation-item');
        const current = document.querySelector('.conversation-item.active');

        if (!conversations.length) return;

        let currentIndex = Array.from(conversations).indexOf(current);
        if (currentIndex === -1) currentIndex = 0;

        const nextIndex = Math.max(0, Math.min(conversations.length - 1, currentIndex + direction));
        const nextConversation = conversations[nextIndex];

        if (nextConversation && nextConversation !== current) {
            nextConversation.click();

            // Scroll para mostrar a conversa selecionada
            nextConversation.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }
    }
}

// Inicializar todos os sistemas quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    new MessageKeyboardShortcuts();
    new TypingIndicator();
    new AdvancedNotifications();
    new MobileGestures();
    window.autoScrollInstance = new AutoScroll();
    new MobileLayout();

    console.log('🚀 Sistemas de usabilidade de mensagens inicializados');
});

// Exportar para uso global
window.MessageUsability = {
    MessageKeyboardShortcuts,
    TypingIndicator,
    AdvancedNotifications,
    MobileGestures
};
