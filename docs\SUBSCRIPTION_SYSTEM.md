# Sistema de Assinaturas VIP - Documentação Completa

## 📋 Visão Geral

Este documento descreve o sistema completo de assinaturas VIP implementado com Stripe e Laravel Cashier, incluindo:

- ✅ Estrutura de planos de assinatura
- ✅ Assinaturas recorrentes com Stripe
- ✅ Interface moderna de planos
- ✅ Webhooks completos para gerenciar ciclo de vida
- ✅ Mudança automática de roles de usuário

## 🚀 Instalação e Configuração

### 1. Executar Setup Automático

```bash
# Executar o comando de setup completo
php artisan subscription:setup

# Ou forçar recriação (se já configurado)
php artisan subscription:setup --force
```

### 2. Configuração Manual (se necessário)

```bash
# Executar migrações
php artisan migrate

# Criar planos de assinatura
php artisan db:seed --class=SubscriptionPlanSeeder

# Configurar produtos no Stripe
php artisan stripe:setup-products
```

## ⚙️ Configuração do Stripe

### 1. Variáveis de Ambiente

Adicione no seu `.env`:

```env
STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
CASHIER_CURRENCY=BRL
```

### 2. Configurar Webhook no Stripe Dashboard

1. Acesse [Stripe Dashboard > Webhooks](https://dashboard.stripe.com/webhooks)
2. Clique em "Add endpoint"
3. URL: `https://seudominio.com/stripe/webhook`
4. Selecione os eventos:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copie o webhook secret e adicione no `.env`

## 📊 Estrutura dos Planos

### Planos Padrão Criados

| Plano | Preço | Intervalo | Trial | Desconto | Benefícios |
|-------|-------|-----------|-------|----------|------------|
| VIP Mensal | R$ 19,90 | 1 mês | 7 dias | - | Acesso básico VIP |
| VIP Trimestral | R$ 49,90 | 3 meses | 7 dias | 15% | R$ 10 carteira + badge |
| VIP Semestral | R$ 89,90 | 6 meses | 14 dias | 25% | R$ 25 carteira + eventos |
| VIP Anual | R$ 159,90 | 1 ano | 14 dias | 35% | R$ 50 carteira + VIP Gold |

### Benefícios por Plano

- **Todos os planos**: Acesso ilimitado, visualização de visitantes, mensagens ilimitadas, destaque no radar
- **Trimestral+**: Créditos na carteira, badges especiais
- **Semestral+**: Acesso prioritário a eventos
- **Anual**: Desconto na loja, suporte VIP exclusivo

## 🔄 Fluxo de Assinatura

### 1. Seleção de Plano
- Usuário acessa `/renovar-vip`
- Visualiza planos disponíveis com preços e benefícios
- Seleciona plano desejado

### 2. Checkout
- Redirecionamento para Stripe Checkout
- Suporte a trial periods
- Coleta de dados de pagamento

### 3. Ativação
- Webhook processa pagamento
- Cria registro local da assinatura
- Atualiza role do usuário para VIP
- Aplica benefícios do plano

### 4. Renovação Automática
- Stripe processa renovações automaticamente
- Webhooks mantêm status sincronizado
- Falhas de pagamento são tratadas

## 🎯 Funcionalidades Implementadas

### ✅ Gestão de Assinaturas
- [x] Criação de assinaturas recorrentes
- [x] Cancelamento com acesso até o fim do período
- [x] Reativação de assinaturas canceladas
- [x] Sincronização automática com Stripe

### ✅ Interface de Usuário
- [x] Página de planos responsiva
- [x] Comparação de preços e benefícios
- [x] Status da assinatura atual
- [x] Botões de ação (cancelar/reativar)

### ✅ Webhooks Implementados
- [x] `checkout.session.completed` - Criação de assinatura
- [x] `customer.subscription.updated` - Atualizações de status
- [x] `customer.subscription.deleted` - Cancelamentos
- [x] `invoice.payment_succeeded` - Renovações bem-sucedidas
- [x] `invoice.payment_failed` - Falhas de pagamento

### ✅ Gestão de Roles
- [x] Upgrade automático para VIP
- [x] Downgrade automático quando expira
- [x] Verificação de múltiplas assinaturas
- [x] Preservação de roles administrativos

## 📁 Arquivos Criados/Modificados

### Novos Modelos
- `app/Models/SubscriptionPlan.php` - Planos de assinatura
- Atualizado `app/Models/VipSubscription.php` - Assinaturas com Stripe
- Atualizado `app/Models/User.php` - Relações de assinatura

### Novos Controllers
- Atualizado `app/Http/Controllers/VipSubscriptionController.php` - Gestão de assinaturas
- Atualizado `app/Http/Controllers/StripeWebhookController.php` - Webhooks completos

### Novos Services
- `app/Services/SubscriptionService.php` - Lógica de assinaturas

### Novos Componentes Livewire
- `app/Livewire/SubscriptionPlans.php` - Interface de planos
- `resources/views/livewire/subscription-plans.blade.php` - View dos planos

### Migrações
- `database/migrations/2025_07_22_000001_create_subscription_plans_table.php`
- `database/migrations/2025_07_22_000002_update_vip_subscriptions_for_plans.php`

### Seeders
- `database/seeders/SubscriptionPlanSeeder.php` - Planos padrão

### Comandos Artisan
- `app/Console/Commands/SetupStripeProducts.php` - Configurar produtos no Stripe
- `app/Console/Commands/SetupSubscriptionSystem.php` - Setup completo

## 🔧 Comandos Úteis

```bash
# Setup completo do sistema
php artisan subscription:setup

# Configurar apenas produtos Stripe
php artisan stripe:setup-products

# Sincronizar status de assinaturas
php artisan cashier:sync

# Verificar webhooks
php artisan cashier:webhook --verify

# Limpar cache
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## 🧪 Testando o Sistema

### 1. Teste Local
1. Configure Stripe em modo test
2. Use cartões de teste do Stripe
3. Monitore logs em `storage/logs/laravel.log`

### 2. Cartões de Teste Stripe
- **Sucesso**: `4242424242424242`
- **Falha**: `4000000000000002`
- **3D Secure**: `4000002500003155`

### 3. Verificar Funcionamento
- [ ] Criação de assinatura
- [ ] Mudança de role para VIP
- [ ] Cancelamento de assinatura
- [ ] Reativação de assinatura
- [ ] Processamento de webhooks
- [ ] Falhas de pagamento

## 🚨 Troubleshooting

### Problemas Comuns

1. **Webhook não funciona**
   - Verificar URL do webhook no Stripe
   - Confirmar STRIPE_WEBHOOK_SECRET no .env
   - Verificar logs de erro

2. **Assinatura não ativa após pagamento**
   - Verificar se webhook foi processado
   - Confirmar criação do registro local
   - Verificar logs do webhook

3. **Role não atualiza**
   - Verificar lógica de atualização no webhook
   - Confirmar que usuário não tem outras assinaturas ativas

### Logs Importantes
```bash
# Logs de webhook
tail -f storage/logs/laravel.log | grep webhook

# Logs de assinatura
tail -f storage/logs/laravel.log | grep subscription

# Logs do Stripe
tail -f storage/logs/laravel.log | grep Stripe
```

## 📈 Próximos Passos Sugeridos

### Melhorias Futuras
- [ ] Notificações por email para falhas de pagamento
- [ ] Dashboard administrativo para métricas
- [ ] Sistema de cupons de desconto
- [ ] Upgrade/downgrade entre planos
- [ ] Pausa temporária de assinatura
- [ ] Relatórios de receita

### Integrações Adicionais
- [ ] Integração com sistema de pontos
- [ ] Benefícios específicos por tempo de assinatura
- [ ] Sistema de referral com descontos
- [ ] Analytics de conversão

## 📞 Suporte

Para dúvidas ou problemas:
1. Verificar logs da aplicação
2. Consultar documentação do Stripe
3. Verificar status dos webhooks no Stripe Dashboard
4. Revisar configurações do Laravel Cashier

---

**Nota**: Este sistema foi implementado seguindo as melhores práticas do Laravel Cashier e Stripe. Todos os pagamentos são processados de forma segura pelo Stripe, e o sistema local mantém apenas referências e status das assinaturas.
