<div>
    <!-- <PERSON><PERSON><PERSON> de Período -->
    <div class="flex flex-wrap gap-2 mb-6">
        <button wire:click="setPeriod('all')" 
                class="px-4 py-2 rounded-lg text-sm font-medium transition-all {{ $period === 'all' ? 'bg-blue-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600' }}">
            🏆 Geral
        </button>
        <button wire:click="setPeriod('monthly')" 
                class="px-4 py-2 rounded-lg text-sm font-medium transition-all {{ $period === 'monthly' ? 'bg-blue-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600' }}">
            📅 Este Mês
        </button>
        <button wire:click="setPeriod('weekly')" 
                class="px-4 py-2 rounded-lg text-sm font-medium transition-all {{ $period === 'weekly' ? 'bg-blue-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600' }}">
            📊 Esta Semana
        </button>
    </div>

    <!-- <PERSON><PERSON><PERSON> do Usuário Atual -->
    @if($currentUserRank && $currentUserRank['position'] > 3)
    <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
                <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
                    {{ $currentUserRank['position'] }}º
                </div>
                <div>
                    <div class="font-semibold text-zinc-300">Sua Posição</div>
                    <div class="text-sm text-zinc-400">{{ $currentUserRank['points'] }} pontos</div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-sm text-zinc-400">de {{ $totalUsers }} usuários</div>
                <div class="text-xs text-zinc-500">Continue participando para subir!</div>
            </div>
        </div>
    </div>
    @endif

    <!-- Lista do Ranking -->
    <div class="space-y-3">
        @forelse($topUsers as $index => $user)
            <div class="flex items-center justify-between p-4 rounded-lg transition-all
                {{ $index < 3 ? 'bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20' : 'bg-zinc-700/50 border border-zinc-600 hover:bg-zinc-700' }}
                {{ auth()->check() && $user->id === auth()->id() ? 'ring-2 ring-blue-500/50' : '' }}">
                
                <div class="flex items-center gap-4">
                    <!-- Posição -->
                    <div class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-bold
                        {{ $index === 0 ? 'bg-yellow-500 text-white' : '' }}
                        {{ $index === 1 ? 'bg-gray-400 text-white' : '' }}
                        {{ $index === 2 ? 'bg-amber-600 text-white' : '' }}
                        {{ $index > 2 ? 'bg-zinc-600 text-zinc-300' : '' }}">
                        @if($index < 3)
                            {{ ['🥇', '🥈', '🥉'][$index] }}
                        @else
                            {{ $index + 1 }}º
                        @endif
                    </div>

                    <!-- Avatar e Info do Usuário -->
                    <div class="flex items-center gap-3">
                        <div class="relative">
                            @php
                                $rankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClassById($user->id);
                            @endphp

                            <div class="ranking-avatar {{ $rankingBorderClass }}">
                                <img src="{{ $user->avatar_url }}"
                                     alt="{{ $user->name }}"
                                     class="w-12 h-12 rounded-full object-cover">
                            </div>

                            <!-- Status VIP -->
                            @if($user->role === 'vip')
                                <div class="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center z-10">
                                    <span class="text-xs">⭐</span>
                                </div>
                            @endif

                            <!-- Indicador Online -->
                            @livewire('user-status-indicator', ['userId' => $user->id], key('status-'.$user->id))
                        </div>
                        
                        <div>
                            <a href="/{{ $user->username }}" 
                               class="font-semibold text-zinc-300 hover:text-white transition-colors" 
                               wire:navigate>
                                {{ $user->name }}
                                @if($user->role === 'vip')
                                    <span class="text-yellow-400 text-xs ml-1">VIP</span>
                                @endif
                            </a>
                            <div class="text-sm text-zinc-400">{{ '@' . $user->username }}</div>
                        </div>
                    </div>
                </div>

                <!-- Pontuação -->
                <div class="text-right">
                    <div class="text-lg font-bold text-zinc-300">
                        {{ number_format($this->getPointsForPeriod($user)) }}
                    </div>
                    <div class="text-sm text-zinc-400">pontos</div>
                </div>
            </div>
        @empty
            <div class="text-center py-8">
                <div class="text-4xl mb-4">🏆</div>
                <div class="text-zinc-400">Nenhum usuário encontrado no ranking</div>
                <div class="text-sm text-zinc-500 mt-2">Seja o primeiro a pontuar!</div>
            </div>
        @endforelse
    </div>

    <!-- Rodapé com Informações -->
    <div class="mt-6 p-4 bg-zinc-700/30 border border-zinc-600 rounded-lg">
        <div class="text-center">
            <div class="text-sm text-zinc-400 mb-2">
                📊 Ranking atualizado em tempo real
            </div>
            <div class="text-xs text-zinc-500">
                Participe mais da comunidade para subir no ranking e ganhar premiações!
            </div>
        </div>
    </div>
</div>
