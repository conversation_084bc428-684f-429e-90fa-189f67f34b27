<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nome do plano (ex: "VIP Mensal")
            $table->string('slug')->unique(); // Slug para URLs (ex: "vip-mensal")
            $table->text('description')->nullable(); // Descrição do plano
            $table->string('stripe_product_id')->nullable(); // ID do produto no Stripe
            $table->string('stripe_price_id')->nullable(); // ID do preço no Stripe
            $table->decimal('price', 10, 2); // Preço do plano
            $table->string('currency', 3)->default('BRL'); // Moeda
            $table->enum('interval', ['day', 'week', 'month', 'year'])->default('month'); // Intervalo de cobrança
            $table->integer('interval_count')->default(1); // Quantidade de intervalos
            $table->integer('trial_period_days')->default(0); // Dias de trial
            $table->json('features')->nullable(); // Features incluídas no plano
            $table->json('benefits')->nullable(); // Benefícios específicos
            $table->boolean('is_active')->default(true); // Se o plano está ativo
            $table->integer('sort_order')->default(0); // Ordem de exibição
            $table->boolean('recommended')->default(false); // Se é recomendado
            $table->boolean('popular')->default(false); // Se é popular
            $table->integer('max_users')->nullable(); // Limite máximo de usuários (null = ilimitado)
            $table->timestamps();

            // Índices
            $table->index(['is_active', 'sort_order']);
            $table->index('stripe_product_id');
            $table->index('stripe_price_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
