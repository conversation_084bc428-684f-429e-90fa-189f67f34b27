/* User Labels System */

/* Original label styles for admin interface */
body {
    font-family: "Comfortaa", sans-serif;
}

.main-wrapper {
    width: 90%;
    max-width: 900px;
    margin: 3em auto;
    text-align: center;
}

.label {
    position: relative;
    margin: 1.5em 3em;
    width: 4em;
    height: 6.2em;
    border-radius: 10px;
    display: inline-block;
    top: 0;
    transition: all 0.2s ease;
}

.label:before,
.label:after {
    position: absolute;
    width: inherit;
    height: inherit;
    border-radius: inherit;
    background: inherit;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.label:before {
    transform: rotate(60deg);
}

.label:after {
    transform: rotate(-60deg);
}

.label:hover {
    top: -4px;
}

.label .circle {
    width: 60px;
    height: 60px;
    position: absolute;
    background: #fff;
    z-index: 10;
    border-radius: 50%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.label .circle i.fa {
    font-size: 2em;
    margin-top: 8px;
}

.label .font {
    display: inline-block;
    margin-top: 1em;
}

.label .ribbon {
    position: absolute;
    border-radius: 4px;
    padding: 5px 5px 4px;
    width: 100px;
    z-index: 11;
    color: #fff;
    bottom: 12px;
    left: 50%;
    margin-left: -55px;
    height: 15px;
    font-size: 14px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.27);
    text-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    background: linear-gradient(to bottom right, #555 0%, #333 100%);
    cursor: default;
}

/* Label color variants for original style */
.label.yellow {
    background: linear-gradient(to bottom right, #ffeb3b 0%, #fbc02d 100%);
    color: #ffb300;
}

.label.orange {
    background: linear-gradient(to bottom right, #ffc107 0%, #f57c00 100%);
    color: #f68401;
}

.label.pink {
    background: linear-gradient(to bottom right, #F48FB1 0%, #d81b60 100%);
    color: #dc306f;
}

.label.red {
    background: linear-gradient(to bottom right, #f4511e 0%, #b71c1c 100%);
    color: #c62828;
}

.label.purple {
    background: linear-gradient(to bottom right, #ab47bc 0%, #4527a0 100%);
    color: #7127a8;
}

.label.teal {
    background: linear-gradient(to bottom right, #4DB6AC 0%, #00796B 100%);
    color: #34a297;
}

.label.blue {
    background: linear-gradient(to bottom right, #4FC3F7 0%, #2196F3 100%);
    color: #259af3;
}

.label.blue-dark {
    background: linear-gradient(to bottom right, #1976D2 0%, #283593 100%);
    color: #1c68c5;
}

.label.green {
    background: linear-gradient(to bottom right, #cddc39 0%, #8bc34a 100%);
    color: #7cb342;
}

.label.green-dark {
    background: linear-gradient(to bottom right, #4CAF50 0%, #1B5E20 100%);
    color: #00944a;
}

.label.silver {
    background: linear-gradient(to bottom right, #E0E0E0 0%, #BDBDBD 100%);
    color: #9e9e9e;
}

.label.gold {
    background: linear-gradient(to bottom right, #e6ce6a 0%, #b7892b 100%);
    color: #b7892b;
}

/* Compact label for profile display */
.user-label {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    white-space: nowrap;
    margin: 0.125rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.user-label:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.user-label .icon {
    width: 0.875rem;
    height: 0.875rem;
    flex-shrink: 0;
}

/* Color variants */
.user-label.blue {
    background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.user-label.green {
    background: linear-gradient(135deg, #10B981 0%, #047857 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.user-label.yellow {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.user-label.red {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.user-label.purple {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.user-label.pink {
    background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(236, 72, 153, 0.3);
}

.user-label.orange {
    background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
}

.user-label.teal {
    background: linear-gradient(135deg, #14B8A6 0%, #0F766E 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(20, 184, 166, 0.3);
}

.user-label.gray {
    background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.user-label.gold {
    background: linear-gradient(135deg, #F59E0B 0%, #B45309 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.4);
    position: relative;
}

.user-label.gold::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: goldShine 2s infinite;
}

.user-label.silver {
    background: linear-gradient(135deg, #9CA3AF 0%, #6B7280 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(156, 163, 175, 0.3);
    position: relative;
}

.user-label.silver::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: silverShine 3s infinite;
}

/* Animations */
@keyframes goldShine {
    0% {
        left: -100%;
    }

    50% {
        left: 100%;
    }

    100% {
        left: 100%;
    }
}

@keyframes silverShine {
    0% {
        left: -100%;
    }

    50% {
        left: 100%;
    }

    100% {
        left: 100%;
    }
}

/* Size variants */
.user-label.small {
    padding: 0.0625rem 0.375rem;
    font-size: 0.625rem;
}

.user-label.small .icon {
    width: 0.75rem;
    height: 0.75rem;
}

.user-label.large {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.user-label.large .icon {
    width: 1rem;
    height: 1rem;
}

/* Container for multiple labels */
.user-labels-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

/* Responsive adjustments */
@media (max-width: 640px) {

    /* Mobile first approach - smaller screens */
    .main-wrapper {
        width: 95%;
        margin: 1.5em auto;
    }

    .label {
        margin: 1em 1.5em;
        width: 3.5em;
        height: 5.5em;
    }

    .label .circle {
        width: 50px;
        height: 50px;
    }

    .label .circle i.fa {
        font-size: 1.5em;
        margin-top: 6px;
    }

    .label .ribbon {
        width: 80px;
        margin-left: -45px;
        font-size: 12px;
        padding: 4px 4px 3px;
    }

    .user-label {
        font-size: 0.625rem;
        padding: 0.0625rem 0.375rem;
        margin: 0.0625rem;
    }

    .user-label .icon {
        width: 0.75rem;
        height: 0.75rem;
    }

    .user-labels-container {
        gap: 0.125rem;
    }
}

@media (min-width: 641px) and (max-width: 768px) {

    /* Tablet adjustments */
    .main-wrapper {
        width: 92%;
    }

    .label {
        margin: 1.2em 2em;
        width: 3.8em;
        height: 5.8em;
    }

    .user-label {
        font-size: 0.6875rem;
        padding: 0.09375rem 0.4375rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {

    /* Small desktop/large tablet */
    .main-wrapper {
        width: 88%;
    }

    .label {
        margin: 1.3em 2.5em;
    }
}

@media (min-width: 1025px) {

    /* Large desktop optimizations */
    .main-wrapper {
        max-width: 1200px;
    }

    .label {
        margin: 1.5em 3.5em;
        width: 4.2em;
        height: 6.5em;
    }

    .user-label.large {
        padding: 0.375rem 1rem;
        font-size: 1rem;
    }

    .user-label.large .icon {
        width: 1.125rem;
        height: 1.125rem;
    }
}

/* Special effects for premium labels */
.user-label.premium {
    background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 50%, #F59E0B 100%);
    background-size: 200% 200%;
    animation: premiumGradient 3s ease infinite;
}

@keyframes premiumGradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* Tooltip for label descriptions */
.user-label[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
}