@props([
    'user',
    'size' => 'normal', // small, normal, large
    'limit' => null // Limitar número de labels exibidas
])

@php
    $labels = $user->activeLabels ?? collect();
    
    if ($limit) {
        $labels = $labels->take($limit);
    }
    
    $sizeClass = match($size) {
        'small' => 'small',
        'large' => 'large',
        default => ''
    };
@endphp

@if($labels->count() > 0)
    <div class="user-labels-container">
        @foreach($labels as $label)
            <span 
                class="user-label {{ $label->color }} {{ $sizeClass }}"
                @if($label->description) title="{{ $label->description }}" @endif
            >
                @if($label->icon)
                    <x-flux::icon :name="$label->icon" class="icon" />
                @endif
                {{ $label->name }}
            </span>
        @endforeach
    </div>
@endif
