/* Melhorias de Usabilidade para Sistema de Mensagens */

/* Indicadores de Digitação */
.typing-indicator-container {
    padding: 0.5rem 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
}

.dark .typing-indicator-container {
    border-top-color: rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.typing-dots {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    margin-right: 0.5rem;
}

.typing-dots::before,
.typing-dots::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #8B5CF6;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots::before {
    animation-delay: 0s;
}

.typing-dots::after {
    animation-delay: 0.2s;
}

.typing-dots {
    position: relative;
}

.typing-dots::before {
    position: absolute;
    left: -8px;
}

.typing-dots::after {
    position: absolute;
    right: -8px;
}

@keyframes typingDots {

    0%,
    80%,
    100% {
        transform: scale(0.8);
        opacity: 0.5;
    }

    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Indicadores de Status Melhorados */
.status-indicator {
    position: relative;
    transition: all 0.3s ease;
}

.status-indicator.online {
    background: #10B981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
    animation: statusPulse 2s infinite;
}

.status-indicator.away {
    background: #F59E0B;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

.status-indicator.dnd {
    background: #EF4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

.status-indicator.offline {
    background: #6B7280;
    box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.3);
}

@keyframes statusPulse {

    0%,
    100% {
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
    }

    50% {
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    }
}

/* Atalhos de Teclado - Indicadores Visuais */
.keyboard-shortcut-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.keyboard-shortcut-hint.show {
    opacity: 1;
    visibility: visible;
}

.kbd-hint {
    display: inline-block;
    padding: 0.125rem 0.375rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.75rem;
    margin: 0 0.125rem;
}

/* Melhorias de Foco e Navegação */
.conversation-item:focus,
.conversation-item.keyboard-selected {
    outline: 2px solid #8B5CF6;
    outline-offset: -2px;
    background: rgba(139, 92, 246, 0.1);
}

.message-input:focus {
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    border-color: #8B5CF6;
}

/* Animações de Feedback */
.message-sent-feedback {
    animation: messageSent 0.3s ease;
}

@keyframes messageSent {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.conversation-item.new-message {
    animation: newMessagePulse 0.5s ease;
}

@keyframes newMessagePulse {

    0%,
    100% {
        background-color: transparent;
    }

    50% {
        background-color: rgba(139, 92, 246, 0.1);
    }
}

/* Gestos Mobile - Indicadores Visuais */
.swipe-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(139, 92, 246, 0.9);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

.swipe-indicator.show {
    opacity: 1;
    visibility: visible;
}

.swipe-indicator.left {
    animation: swipeLeft 0.5s ease;
}

.swipe-indicator.right {
    animation: swipeRight 0.5s ease;
}

@keyframes swipeLeft {
    0% {
        transform: translate(-50%, -50%) translateX(20px);
    }

    100% {
        transform: translate(-50%, -50%) translateX(-20px);
    }
}

@keyframes swipeRight {
    0% {
        transform: translate(-50%, -50%) translateX(-20px);
    }

    100% {
        transform: translate(-50%, -50%) translateX(20px);
    }
}

/* Notificações Melhoradas */
.notification-badge {
    position: relative;
    overflow: visible;
}

.notification-badge.has-unread::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #EF4444;
    border-radius: 50%;
    border: 2px solid white;
    animation: notificationPulse 2s infinite;
}

@keyframes notificationPulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* Estados de Loading Melhorados */
.message-loading {
    opacity: 0.6;
    position: relative;
}

.message-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0.5rem;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: messageLoading 1s linear infinite;
}

@keyframes messageLoading {
    0% {
        transform: translateY(-50%) rotate(0deg);
    }

    100% {
        transform: translateY(-50%) rotate(360deg);
    }
}

/* Melhorias de Acessibilidade */
@media (prefers-reduced-motion: reduce) {

    .typing-dots::before,
    .typing-dots::after,
    .status-indicator.online,
    .notification-badge.has-unread::after,
    .message-loading::after {
        animation: none;
    }

    .conversation-item,
    .message-bubble,
    .swipe-indicator {
        transition: none;
    }
}

/* Alto Contraste */
@media (prefers-contrast: high) {
    .typing-indicator-container {
        border-top: 2px solid;
        background: transparent;
    }

    .status-indicator {
        border: 2px solid white;
    }

    .conversation-item:focus,
    .conversation-item.keyboard-selected {
        outline: 3px solid;
        background: transparent;
    }
}

/* Modo Escuro Específico */
.dark .keyboard-shortcut-hint {
    background: rgba(255, 255, 255, 0.9);
    color: black;
}

.dark .kbd-hint {
    background: rgba(0, 0, 0, 0.2);
}

.dark .notification-badge.has-unread::after {
    border-color: rgb(39 39 42);
}

/* Responsividade para Indicadores */
@media (max-width: 640px) {
    .typing-indicator-container {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    .keyboard-shortcut-hint {
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        max-width: 90vw;
    }

    .swipe-indicator {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .status-indicator {
        width: 0.625rem;
        height: 0.625rem;
    }
}

/* Animações de Entrada para Mensagens */
.message-bubble.new {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Indicador de Scroll */
.scroll-indicator {
    position: fixed;
    bottom: 6rem;
    right: 1rem;
    background: rgba(139, 92, 246, 0.9);
    color: white;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-indicator.show {
    opacity: 1;
    visibility: visible;
}

.scroll-indicator:hover {
    background: rgba(139, 92, 246, 1);
    transform: scale(1.1);
}

/* Feedback Visual para Ações */
.action-feedback {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.action-feedback.show {
    opacity: 1;
    visibility: visible;
}

.action-feedback.error {
    background: rgba(239, 68, 68, 0.9);
}

.action-feedback.warning {
    background: rgba(245, 158, 11, 0.9);
}

/* Indicador de Nova Mensagem */
.new-message-indicator {
    position: fixed;
    bottom: 6rem;
    right: 1rem;
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.new-message-indicator:hover {
    transform: translateY(0) scale(1.05);
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.5);
}

.new-message-indicator.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Animação de pulse para o indicador */
.new-message-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    border-radius: 2rem;
    z-index: -1;
    animation: indicatorPulse 2s infinite;
}

@keyframes indicatorPulse {

    0%,
    100% {
        opacity: 0.7;
        transform: scale(1);
    }

    50% {
        opacity: 0.3;
        transform: scale(1.1);
    }
}

/* Layout Mobile Horizontal - Melhorias */
@media (max-width: 640px) {
    .new-message-indicator {
        bottom: 4rem;
        right: 0.75rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Melhorias para scroll horizontal */
    .conversations-sidebar.has-scroll-indicators {
        position: relative;
    }

    .conversations-sidebar.has-scroll-indicators::before,
    .conversations-sidebar.has-scroll-indicators::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 15px;
        pointer-events: none;
        z-index: 10;
    }

    .conversations-sidebar.has-scroll-indicators::before {
        left: 0;
        background: linear-gradient(to right, rgba(255, 255, 255, 0.9), transparent);
    }

    .conversations-sidebar.has-scroll-indicators::after {
        right: 0;
        background: linear-gradient(to left, rgba(255, 255, 255, 0.9), transparent);
    }

    /* Indicador de conversa ativa melhorado */
    .conversation-item.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 3px;
        background: #8B5CF6;
        border-radius: 2px;
    }

    /* Animação de entrada para conversas */
    .conversation-item {
        animation: slideInFromTop 0.3s ease-out;
    }

    .conversation-item:nth-child(1) {
        animation-delay: 0.1s;
    }

    .conversation-item:nth-child(2) {
        animation-delay: 0.2s;
    }

    .conversation-item:nth-child(3) {
        animation-delay: 0.3s;
    }

    .conversation-item:nth-child(4) {
        animation-delay: 0.4s;
    }

    .conversation-item:nth-child(5) {
        animation-delay: 0.5s;
    }
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Melhorias para área de mensagens no mobile */
@media (max-width: 640px) {
    .chat-area .messages-scroll-area {
        height: calc(100vh - 12rem);
        padding: 1rem 0.75rem;
    }

    .message-input-area {
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(139, 92, 246, 0.2);
    }

    .dark .message-input-area {
        background: rgba(39, 39, 42, 0.95);
        border-top-color: rgba(139, 92, 246, 0.3);
    }

    /* Botão de envio melhorado no mobile */
    .send-button {
        min-width: 3rem;
        min-height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
        transition: all 0.3s ease;
    }

    .send-button:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    }

    .send-button:active {
        transform: scale(0.95);
    }
}