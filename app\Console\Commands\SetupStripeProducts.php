<?php

namespace App\Console\Commands;

use App\Models\SubscriptionPlan;
use Illuminate\Console\Command;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;

class SetupStripeProducts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'stripe:setup-products {--force : Force recreation of existing products}';

    /**
     * The console command description.
     */
    protected $description = 'Setup Stripe products and prices for subscription plans';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Configurar Stripe
        Stripe::setApiKey(config('cashier.secret'));

        if (!config('cashier.secret')) {
            $this->error('Stripe secret key not configured. Please set STRIPE_SECRET in your .env file.');
            return 1;
        }

        $this->info('Setting up Stripe products and prices...');

        $plans = SubscriptionPlan::all();

        if ($plans->isEmpty()) {
            $this->warn('No subscription plans found. Please run the seeder first:');
            $this->line('php artisan db:seed --class=SubscriptionPlanSeeder');
            return 1;
        }

        foreach ($plans as $plan) {
            $this->info("Processing plan: {$plan->name}");

            try {
                // Criar ou atualizar produto no Stripe
                $product = $this->createOrUpdateProduct($plan);
                
                // Criar ou atualizar preço no Stripe
                $price = $this->createOrUpdatePrice($plan, $product);

                // Atualizar o plano com os IDs do Stripe
                $plan->update([
                    'stripe_product_id' => $product->id,
                    'stripe_price_id' => $price->id,
                ]);

                $this->line("✓ Product: {$product->id}");
                $this->line("✓ Price: {$price->id}");
                $this->newLine();

            } catch (\Exception $e) {
                $this->error("Failed to setup plan {$plan->name}: " . $e->getMessage());
                continue;
            }
        }

        $this->info('Stripe products and prices setup completed!');
        return 0;
    }

    /**
     * Create or update a Stripe product
     */
    private function createOrUpdateProduct(SubscriptionPlan $plan)
    {
        $productData = [
            'name' => $plan->name,
            'description' => $plan->description,
            'metadata' => [
                'plan_id' => $plan->id,
                'plan_slug' => $plan->slug,
            ],
        ];

        // Se já existe um produto, tentar atualizar
        if ($plan->stripe_product_id && !$this->option('force')) {
            try {
                $product = Product::retrieve($plan->stripe_product_id);
                $product = Product::update($plan->stripe_product_id, $productData);
                $this->line("Updated existing product: {$product->id}");
                return $product;
            } catch (\Exception $e) {
                $this->warn("Could not update existing product, creating new one: " . $e->getMessage());
            }
        }

        // Criar novo produto
        $product = Product::create($productData);
        $this->line("Created new product: {$product->id}");
        return $product;
    }

    /**
     * Create or update a Stripe price
     */
    private function createOrUpdatePrice(SubscriptionPlan $plan, $product)
    {
        $priceData = [
            'product' => $product->id,
            'unit_amount' => (int)($plan->price * 100), // Convert to cents
            'currency' => strtolower($plan->currency),
            'recurring' => [
                'interval' => $plan->interval,
                'interval_count' => $plan->interval_count,
            ],
            'metadata' => [
                'plan_id' => $plan->id,
                'plan_slug' => $plan->slug,
            ],
        ];

        // Se já existe um preço, verificar se precisa criar novo
        if ($plan->stripe_price_id && !$this->option('force')) {
            try {
                $existingPrice = Price::retrieve($plan->stripe_price_id);
                
                // Verificar se o preço mudou
                if ($existingPrice->unit_amount == $priceData['unit_amount'] &&
                    $existingPrice->currency == $priceData['currency'] &&
                    $existingPrice->recurring->interval == $priceData['recurring']['interval'] &&
                    $existingPrice->recurring->interval_count == $priceData['recurring']['interval_count']) {
                    
                    $this->line("Price unchanged, using existing: {$existingPrice->id}");
                    return $existingPrice;
                }
                
                // Se o preço mudou, desativar o antigo e criar novo
                Price::update($plan->stripe_price_id, ['active' => false]);
                $this->line("Deactivated old price: {$plan->stripe_price_id}");
                
            } catch (\Exception $e) {
                $this->warn("Could not check existing price: " . $e->getMessage());
            }
        }

        // Criar novo preço
        $price = Price::create($priceData);
        $this->line("Created new price: {$price->id}");
        return $price;
    }
}
