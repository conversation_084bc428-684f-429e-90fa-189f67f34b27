<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\VipSubscription;
use App\Models\SubscriptionPlan;
use App\Services\VipSubscriptionService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Carbon\Carbon;

class VipSubscriptionController extends Controller
{
    protected $vipSubscriptionService;
    protected $subscriptionService;

    public function __construct(VipSubscriptionService $vipSubscriptionService, SubscriptionService $subscriptionService)
    {
        $this->vipSubscriptionService = $vipSubscriptionService;
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Create a checkout session for VIP subscription
     */
    public function createCheckoutSession(Request $request)
    {
        $validated = $request->validate([
            'plan_slug' => 'required|string|exists:subscription_plans,slug',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::where('slug', $validated['plan_slug'])
            ->where('is_active', true)
            ->firstOrFail();

        // Check if user already has an active subscription
        if ($user->hasActiveVipSubscription()) {
            return redirect()->route('renovar-vip')
                ->with('info', 'Você já possui uma assinatura VIP ativa.');
        }

        try {
            logger()->info('Iniciando criação de assinatura recorrente VIP', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_slug' => $plan->slug,
                'plan_price' => $plan->price
            ]);

            // Ensure user is a Stripe customer
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer([
                    'name' => $user->name,
                    'email' => $user->email,
                ]);
            }

            // Create checkout session for subscription
            Stripe::setApiKey(config('cashier.secret'));

            $sessionData = [
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price' => $plan->stripe_price_id,
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => route('vip.payment.success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('vip.payment.cancel'),
                'customer' => $user->stripe_id,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'plan_slug' => $plan->slug,
                ],
            ];

            // Add trial if plan has trial period
            if ($plan->trial_period_days > 0) {
                $sessionData['subscription_data'] = [
                    'trial_period_days' => $plan->trial_period_days,
                ];
            }

            $session = Session::create($sessionData);

            logger()->info('Sessão de checkout de assinatura criada com sucesso', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'session_id' => $session->id,
                'session_url' => $session->url
            ]);

            return redirect($session->url);

        } catch (ApiErrorException $e) {
            logger()->error('Erro na API do Stripe ao criar sessão de assinatura', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'stripe_code' => $e->getStripeCode() ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            $errorMessage = $this->getStripeErrorMessage($e);
            return redirect()->route('renovar-vip')->with('error', $errorMessage);

        } catch (\Exception $e) {
            logger()->error('Erro geral ao criar sessão de assinatura', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('renovar-vip')
                ->with('error', 'Ocorreu um erro interno ao processar o pagamento. Por favor, tente novamente em alguns minutos.');
        }
    }

    /**
     * Handle successful payment
     */
    public function paymentSuccess(Request $request)
    {
        $sessionId = $request->get('session_id');

        if (!$sessionId) {
            return redirect()->route('renovar-vip')
                ->with('error', 'Sessão de pagamento não encontrada.');
        }

        try {
            Stripe::setApiKey(config('cashier.secret'));
            $session = Session::retrieve($sessionId);

            $user = Auth::user();

            // Verify the session belongs to the current user
            if ($session->metadata->user_id != $user->id) {
                return redirect()->route('renovar-vip')
                    ->with('error', 'Sessão de pagamento inválida.');
            }

            // The subscription should be automatically created by Stripe webhook
            // But we can also handle it here as a fallback
            if ($session->subscription) {
                $stripeSubscription = \Stripe\Subscription::retrieve($session->subscription);

                // Check if we already have this subscription in our database
                $localSubscription = VipSubscription::where('stripe_subscription_id', $stripeSubscription->id)->first();

                if (!$localSubscription) {
                    // Create local subscription record
                    $plan = SubscriptionPlan::find($session->metadata->plan_id);

                    if ($plan) {
                        VipSubscription::create([
                            'user_id' => $user->id,
                            'plan_id' => $plan->id,
                            'plan_days' => $this->calculatePlanDays($plan),
                            'amount' => $plan->price,
                            'status' => $this->mapStripeStatus($stripeSubscription->status),
                            'stripe_subscription_id' => $stripeSubscription->id,
                            'stripe_customer_id' => $user->stripe_id,
                            'activated_at' => $stripeSubscription->status === 'active' ? Carbon::now() : null,
                            'trial_ends_at' => $stripeSubscription->trial_end ? Carbon::createFromTimestamp($stripeSubscription->trial_end) : null,
                            'expires_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                        ]);

                        // Update user role
                        if (in_array($stripeSubscription->status, ['active', 'trialing'])) {
                            $user->update(['role' => $plan->user_role]);
                        }
                    }
                }
            }

            return redirect()->route('renovar-vip')
                ->with('success', 'Assinatura VIP ativada com sucesso! Bem-vindo ao clube VIP.');

        } catch (\Exception $e) {
            logger()->error('Erro ao processar sucesso do pagamento', [
                'session_id' => $sessionId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('renovar-vip')
                ->with('error', 'Erro ao confirmar pagamento. Entre em contato com o suporte.');
        }
    }

    /**
     * Handle cancelled payment
     */
    public function paymentCancel(Request $request)
    {
        return redirect()->route('renovar-vip')
            ->with('info', 'Pagamento cancelado. Você pode tentar novamente a qualquer momento.');
    }

    /**
     * Cancel user subscription
     */
    public function cancelSubscription(Request $request)
    {
        $user = Auth::user();
        $subscription = $user->activeVipSubscription;

        if (!$subscription) {
            return redirect()->route('renovar-vip')
                ->with('error', 'Nenhuma assinatura ativa encontrada.');
        }

        try {
            $this->subscriptionService->cancelSubscription($subscription, false);

            return redirect()->route('renovar-vip')
                ->with('success', 'Assinatura cancelada. Você continuará tendo acesso VIP até o final do período pago.');

        } catch (\Exception $e) {
            logger()->error('Erro ao cancelar assinatura', [
                'subscription_id' => $subscription->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('renovar-vip')
                ->with('error', 'Erro ao cancelar assinatura. Tente novamente ou entre em contato com o suporte.');
        }
    }

    /**
     * Resume user subscription
     */
    public function resumeSubscription(Request $request)
    {
        $user = Auth::user();
        $subscription = VipSubscription::where('user_id', $user->id)
            ->where('status', VipSubscription::STATUS_CANCELED)
            ->whereNotNull('stripe_subscription_id')
            ->latest()
            ->first();

        if (!$subscription) {
            return redirect()->route('renovar-vip')
                ->with('error', 'Nenhuma assinatura cancelada encontrada.');
        }

        try {
            $this->subscriptionService->resumeSubscription($subscription);

            return redirect()->route('renovar-vip')
                ->with('success', 'Assinatura reativada com sucesso!');

        } catch (\Exception $e) {
            logger()->error('Erro ao reativar assinatura', [
                'subscription_id' => $subscription->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('renovar-vip')
                ->with('error', 'Erro ao reativar assinatura. Tente novamente ou entre em contato com o suporte.');
        }
    }

    /**
     * Calculate plan days based on interval
     */
    private function calculatePlanDays(SubscriptionPlan $plan): int
    {
        return match ($plan->interval) {
            'day' => $plan->interval_count,
            'week' => $plan->interval_count * 7,
            'month' => $plan->interval_count * 30,
            'year' => $plan->interval_count * 365,
            default => 30,
        };
    }

    /**
     * Map Stripe subscription status to local status
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'active' => VipSubscription::STATUS_ACTIVE,
            'trialing' => VipSubscription::STATUS_TRIALING,
            'canceled' => VipSubscription::STATUS_CANCELED,
            'incomplete' => VipSubscription::STATUS_PENDING,
            'incomplete_expired' => VipSubscription::STATUS_EXPIRED,
            'past_due' => VipSubscription::STATUS_PAST_DUE,
            'unpaid' => VipSubscription::STATUS_UNPAID,
            default => VipSubscription::STATUS_PENDING,
        };
    }

    /**
     * Get user-friendly error message from Stripe exception
     */
    private function getStripeErrorMessage(ApiErrorException $e): string
    {
        $stripeCode = $e->getStripeCode();
        $message = $e->getMessage();

        switch ($stripeCode) {
            case 'card_declined':
                return 'Cartão recusado. Verifique os dados ou use outro cartão.';
            case 'expired_card':
                return 'Cartão expirado. Use um cartão válido.';
            case 'incorrect_cvc':
                return 'Código de segurança incorreto.';
            case 'incorrect_number':
                return 'Número do cartão inválido.';
            case 'invalid_expiry_month':
            case 'invalid_expiry_year':
                return 'Data de validade inválida.';
            case 'insufficient_funds':
                return 'Cartão recusado por fundos insuficientes.';
            case 'processing_error':
                return 'Erro no processamento do pagamento. Tente novamente em alguns minutos.';
            case 'rate_limit':
                return 'Muitas tentativas. Aguarde alguns minutos antes de tentar novamente.';
            case 'api_key_expired':
            case 'invalid_api_key':
                return 'Erro de configuração do sistema de pagamento. Entre em contato com o suporte.';
            default:
                // Para outros erros, verificar se contém palavras-chave específicas
                if (str_contains(strtolower($message), 'network') || str_contains(strtolower($message), 'connection')) {
                    return 'Erro de conexão. Verifique sua internet e tente novamente.';
                } elseif (str_contains(strtolower($message), 'timeout')) {
                    return 'Tempo limite excedido. Tente novamente.';
                } elseif (str_contains(strtolower($message), 'invalid')) {
                    return 'Dados inválidos fornecidos. Verifique as informações e tente novamente.';
                }

                return 'Ocorreu um erro ao processar o pagamento. Por favor, tente novamente.';
        }
    }
}
