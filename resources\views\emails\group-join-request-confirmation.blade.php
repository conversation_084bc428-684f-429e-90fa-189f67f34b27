<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitação Enviada - {{ $group->name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #00FFF7;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #E60073;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .success-icon {
            text-align: center;
            font-size: 48px;
            margin: 20px 0;
        }
        .group-info {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .group-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .group-description {
            opacity: 0.9;
            margin-bottom: 10px;
        }
        .group-stats {
            font-size: 14px;
            opacity: 0.8;
        }
        .group-stats span {
            margin-right: 15px;
        }
        .status-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #00FFF7, #E60073);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 255, 247, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ config('app.name') }}</div>
            <h1 style="color: #333; margin: 0;">Solicitação Enviada</h1>
        </div>

        <div class="content">
            <div class="success-icon">✅</div>
            
            <p>Olá <strong>{{ $user->name }}</strong>,</p>
            
            <p>Sua solicitação para entrar no grupo foi enviada com sucesso!</p>

            <div class="group-info">
                <div class="group-name">{{ $group->name }}</div>
                @if($group->description)
                    <div class="group-description">{{ $group->description }}</div>
                @endif
                <div class="group-stats">
                    <span>👥 {{ $group->members_count }} {{ $group->members_count == 1 ? 'membro' : 'membros' }}</span>
                    <span>🔒 Grupo Privado</span>
                </div>
            </div>

            <div class="status-info">
                <strong>⏳ Status:</strong> Aguardando aprovação<br>
                <strong>📧 Próximos passos:</strong> Os administradores do grupo foram notificados e irão analisar sua solicitação.
            </div>

            <p>Você receberá uma notificação por email assim que sua solicitação for aprovada ou rejeitada pelos administradores do grupo.</p>

            <div class="button-container">
                <a href="{{ route('grupos.show', $group->slug) }}" class="btn btn-primary">
                    👁️ Ver Grupo
                </a>
                <a href="{{ route('grupos.invitations.index') }}" class="btn btn-secondary">
                    📋 Minhas Solicitações
                </a>
            </div>

            <p style="font-size: 14px; color: #666; margin-top: 30px;">
                <strong>Dica:</strong> Enquanto aguarda a aprovação, você pode explorar outros grupos públicos 
                ou verificar se há grupos que correspondem aos seus interesses.
            </p>
        </div>

        <div class="footer">
            <p>Este é um email automático do sistema {{ config('app.name') }}.</p>
            <p>Obrigado por fazer parte da nossa comunidade!</p>
        </div>
    </div>
</body>
</html>
