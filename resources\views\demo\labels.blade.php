<x-layouts.app title="Demo das Labels de Usuários">
    <div class="max-w-4xl mx-auto p-6">
        <h1 class="text-3xl font-bold text-gray-300 mb-8 text-center">Sistema de Labels de Usuários</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- VIP -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">VIP</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label gold">
                        <x-flux::icon name="star" class="icon" />
                        VIP
                    </span>
                </div>
                <p class="text-sm text-gray-400">Usuário VIP com benefícios especiais</p>
            </div>

            <!-- Moderador -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Moderador</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label blue">
                        <x-flux::icon name="shield-check" class="icon" />
                        Moderador
                    </span>
                </div>
                <p class="text-sm text-gray-400">Moderador da comunidade</p>
            </div>

            <!-- Verificado -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Verificado</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label green">
                        <x-flux::icon name="check-badge" class="icon" />
                        Verificado
                    </span>
                </div>
                <p class="text-sm text-gray-400">Perfil verificado</p>
            </div>

            <!-- Criador -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Criador</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label purple">
                        <x-flux::icon name="sparkles" class="icon" />
                        Criador
                    </span>
                </div>
                <p class="text-sm text-gray-400">Criador de conteúdo</p>
            </div>

            <!-- Beta Tester -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Beta Tester</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label orange">
                        <x-flux::icon name="beaker" class="icon" />
                        Beta Tester
                    </span>
                </div>
                <p class="text-sm text-gray-400">Testador de funcionalidades beta</p>
            </div>

            <!-- Influencer -->
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-4">Influencer</h3>
                <div class="flex justify-center mb-4">
                    <span class="user-label pink">
                        <x-flux::icon name="megaphone" class="icon" />
                        Influencer
                    </span>
                </div>
                <p class="text-sm text-gray-400">Influenciador da plataforma</p>
            </div>
        </div>

        <!-- Tamanhos diferentes -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-300 mb-6 text-center">Tamanhos Disponíveis</h2>
            
            <div class="bg-zinc-800 p-6 rounded-lg">
                <div class="flex items-center justify-center space-x-8">
                    <!-- Small -->
                    <div class="text-center">
                        <span class="user-label gold small mb-2">
                            <x-flux::icon name="star" class="icon" />
                            VIP
                        </span>
                        <p class="text-xs text-gray-400 mt-2">Small</p>
                    </div>

                    <!-- Normal -->
                    <div class="text-center">
                        <span class="user-label gold mb-2">
                            <x-flux::icon name="star" class="icon" />
                            VIP
                        </span>
                        <p class="text-xs text-gray-400 mt-2">Normal</p>
                    </div>

                    <!-- Large -->
                    <div class="text-center">
                        <span class="user-label gold large mb-2">
                            <x-flux::icon name="star" class="icon" />
                            VIP
                        </span>
                        <p class="text-xs text-gray-400 mt-2">Large</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Múltiplas labels -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-300 mb-6 text-center">Múltiplas Labels</h2>
            
            <div class="bg-zinc-800 p-6 rounded-lg text-center">
                <div class="user-labels-container justify-center">
                    <span class="user-label gold">
                        <x-flux::icon name="star" class="icon" />
                        VIP
                    </span>
                    <span class="user-label green">
                        <x-flux::icon name="check-badge" class="icon" />
                        Verificado
                    </span>
                    <span class="user-label purple">
                        <x-flux::icon name="sparkles" class="icon" />
                        Criador
                    </span>
                </div>
                <p class="text-sm text-gray-400 mt-4">Exemplo de usuário com múltiplas labels</p>
            </div>
        </div>

        <!-- Código de exemplo -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-300 mb-6 text-center">Como Usar</h2>
            
            <div class="bg-zinc-900 p-6 rounded-lg">
                <pre class="text-green-400 text-sm overflow-x-auto"><code>&lt;!-- Componente Blade (Recomendado) --&gt;
&lt;x-user-labels :user="$user" /&gt;

&lt;!-- Com tamanho específico --&gt;
&lt;x-user-labels :user="$user" size="small" /&gt;

&lt;!-- Com limite de labels --&gt;
&lt;x-user-labels :user="$user" :limit="3" /&gt;

&lt;!-- HTML Manual --&gt;
&lt;span class="user-label gold"&gt;
    &lt;x-flux::icon name="star" class="icon" /&gt;
    VIP
&lt;/span&gt;</code></pre>
            </div>
        </div>

        <!-- Link para administração -->
        @if(auth()->user()->role === 'admin')
            <div class="mt-12 text-center">
                <a href="{{ route('admin.labels') }}" 
                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition">
                    <x-flux::icon name="cog" class="w-5 h-5 mr-2" />
                    Gerenciar Labels
                </a>
            </div>
        @endif
    </div>
</x-layouts.app>
