/**
 * Configuração de Notificações Avançadas
 * Sistema de notificações em tempo real com sons e configurações personalizáveis
 */

// Configuração de sons de notificação
const NotificationSounds = {
    message: {
        url: '/sounds/message.mp3',
        volume: 0.5,
        description: 'Som para novas mensagens'
    },
    mention: {
        url: '/sounds/mention.mp3',
        volume: 0.7,
        description: 'Som para menções'
    },
    notification: {
        url: '/sounds/notification.mp3',
        volume: 0.4,
        description: 'Som para notificações gerais'
    },
    typing: {
        url: '/sounds/typing.mp3',
        volume: 0.2,
        description: 'Som para indicador de digitação'
    }
};

// Configuração de tipos de notificação
const NotificationTypes = {
    message: {
        icon: '💬',
        color: '#8B5CF6',
        priority: 'normal',
        sound: 'message',
        vibrate: [200, 100, 200]
    },
    mention: {
        icon: '📢',
        color: '#F59E0B',
        priority: 'high',
        sound: 'mention',
        vibrate: [300, 100, 300, 100, 300]
    },
    like: {
        icon: '❤️',
        color: '#EF4444',
        priority: 'low',
        sound: 'notification',
        vibrate: [100]
    },
    follow: {
        icon: '👤',
        color: '#10B981',
        priority: 'normal',
        sound: 'notification',
        vibrate: [200]
    },
    system: {
        icon: '⚙️',
        color: '#6B7280',
        priority: 'normal',
        sound: 'notification',
        vibrate: [100, 50, 100]
    }
};

// Configurações padrão do usuário
const DefaultUserSettings = {
    soundEnabled: true,
    browserNotificationsEnabled: false,
    vibrationEnabled: true,
    doNotDisturbMode: false,
    doNotDisturbStart: '22:00',
    doNotDisturbEnd: '08:00',
    notificationTypes: {
        message: true,
        mention: true,
        like: true,
        follow: true,
        system: true
    },
    soundVolume: 0.5,
    showPreview: true,
    groupNotifications: true,
    maxNotifications: 5
};

// Classe para gerenciar configurações de notificação
class NotificationSettings {
    constructor() {
        this.settings = this.loadSettings();
        this.init();
    }

    init() {
        // Aplicar configurações salvas
        this.applySettings();
        
        // Escutar mudanças nas configurações
        this.setupSettingsListeners();
        
        console.log('⚙️ Configurações de notificação carregadas:', this.settings);
    }

    loadSettings() {
        const saved = localStorage.getItem('notification-settings');
        return saved ? { ...DefaultUserSettings, ...JSON.parse(saved) } : DefaultUserSettings;
    }

    saveSettings() {
        localStorage.setItem('notification-settings', JSON.stringify(this.settings));
        this.applySettings();
    }

    updateSetting(key, value) {
        if (key.includes('.')) {
            const keys = key.split('.');
            let obj = this.settings;
            for (let i = 0; i < keys.length - 1; i++) {
                obj = obj[keys[i]];
            }
            obj[keys[keys.length - 1]] = value;
        } else {
            this.settings[key] = value;
        }
        this.saveSettings();
    }

    getSetting(key) {
        if (key.includes('.')) {
            const keys = key.split('.');
            let obj = this.settings;
            for (const k of keys) {
                obj = obj[k];
                if (obj === undefined) return undefined;
            }
            return obj;
        }
        return this.settings[key];
    }

    applySettings() {
        // Aplicar volume dos sons
        Object.keys(NotificationSounds).forEach(soundKey => {
            NotificationSounds[soundKey].volume = this.settings.soundVolume;
        });

        // Aplicar configurações de vibração
        if (!this.settings.vibrationEnabled) {
            Object.keys(NotificationTypes).forEach(type => {
                NotificationTypes[type].vibrate = [];
            });
        }
    }

    setupSettingsListeners() {
        // Escutar mudanças no modo não perturbe baseado no horário
        setInterval(() => {
            this.checkDoNotDisturbMode();
        }, 60000); // Verificar a cada minuto
    }

    checkDoNotDisturbMode() {
        if (!this.settings.doNotDisturbStart || !this.settings.doNotDisturbEnd) return;

        const now = new Date();
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');

        const start = this.settings.doNotDisturbStart;
        const end = this.settings.doNotDisturbEnd;

        let inDoNotDisturb = false;

        if (start <= end) {
            // Mesmo dia (ex: 08:00 - 22:00)
            inDoNotDisturb = currentTime >= start && currentTime <= end;
        } else {
            // Atravessa meia-noite (ex: 22:00 - 08:00)
            inDoNotDisturb = currentTime >= start || currentTime <= end;
        }

        if (inDoNotDisturb !== this.settings.doNotDisturbMode) {
            this.settings.doNotDisturbMode = inDoNotDisturb;
            console.log(`🔕 Modo não perturbe ${inDoNotDisturb ? 'ativado' : 'desativado'}`);
        }
    }

    isNotificationAllowed(type) {
        // Verificar se o tipo de notificação está habilitado
        if (!this.getSetting(`notificationTypes.${type}`)) return false;

        // Verificar modo não perturbe
        if (this.settings.doNotDisturbMode) {
            // Permitir apenas notificações de alta prioridade
            return NotificationTypes[type]?.priority === 'high';
        }

        return true;
    }

    shouldPlaySound(type) {
        return this.settings.soundEnabled && 
               this.isNotificationAllowed(type) && 
               !this.settings.doNotDisturbMode;
    }

    shouldShowBrowserNotification(type) {
        return this.settings.browserNotificationsEnabled && 
               this.isNotificationAllowed(type) &&
               !document.hasFocus();
    }

    shouldVibrate(type) {
        return this.settings.vibrationEnabled && 
               this.isNotificationAllowed(type) &&
               'vibrate' in navigator;
    }

    // Método para criar interface de configurações
    createSettingsUI() {
        return `
            <div class="notification-settings p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold mb-4">Configurações de Notificação</h3>
                
                <div class="space-y-4">
                    <!-- Som -->
                    <div class="flex items-center justify-between">
                        <label class="text-sm font-medium">Sons de notificação</label>
                        <input type="checkbox" ${this.settings.soundEnabled ? 'checked' : ''} 
                               onchange="notificationSettings.updateSetting('soundEnabled', this.checked)">
                    </div>
                    
                    <!-- Volume -->
                    <div class="flex items-center justify-between">
                        <label class="text-sm font-medium">Volume</label>
                        <input type="range" min="0" max="1" step="0.1" value="${this.settings.soundVolume}"
                               onchange="notificationSettings.updateSetting('soundVolume', parseFloat(this.value))">
                    </div>
                    
                    <!-- Notificações do browser -->
                    <div class="flex items-center justify-between">
                        <label class="text-sm font-medium">Notificações do navegador</label>
                        <input type="checkbox" ${this.settings.browserNotificationsEnabled ? 'checked' : ''} 
                               onchange="notificationSettings.updateSetting('browserNotificationsEnabled', this.checked)">
                    </div>
                    
                    <!-- Vibração -->
                    <div class="flex items-center justify-between">
                        <label class="text-sm font-medium">Vibração</label>
                        <input type="checkbox" ${this.settings.vibrationEnabled ? 'checked' : ''} 
                               onchange="notificationSettings.updateSetting('vibrationEnabled', this.checked)">
                    </div>
                    
                    <!-- Modo não perturbe -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium">Modo não perturbe</label>
                        <div class="flex gap-2">
                            <input type="time" value="${this.settings.doNotDisturbStart}" 
                                   onchange="notificationSettings.updateSetting('doNotDisturbStart', this.value)">
                            <span class="text-sm text-gray-500">até</span>
                            <input type="time" value="${this.settings.doNotDisturbEnd}" 
                                   onchange="notificationSettings.updateSetting('doNotDisturbEnd', this.value)">
                        </div>
                    </div>
                    
                    <!-- Tipos de notificação -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium">Tipos de notificação</label>
                        ${Object.keys(NotificationTypes).map(type => `
                            <div class="flex items-center justify-between">
                                <span class="text-sm">${NotificationTypes[type].icon} ${type}</span>
                                <input type="checkbox" ${this.getSetting(`notificationTypes.${type}`) ? 'checked' : ''} 
                                       onchange="notificationSettings.updateSetting('notificationTypes.${type}', this.checked)">
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
}

// Exportar configurações
window.NotificationSounds = NotificationSounds;
window.NotificationTypes = NotificationTypes;
window.NotificationSettings = NotificationSettings;

// Inicializar configurações globalmente
window.notificationSettings = new NotificationSettings();

console.log('🔔 Sistema de configuração de notificações carregado');
