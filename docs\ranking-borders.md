s# Sistema de Bordas de Ranking

Este sistema adiciona bordas visuais especiais aos avatares dos usuários baseadas em sua posição no ranking de pontos.

## Tipos de Bordas

### 🥉 Bronze (Posições 11-20)
- Borda dourada simples com brilho sutil
- Indicador triangular na parte inferior

### 🥈 Prata (Posições 4-10)
- Borda prateada com brilho mais intenso
- Indicador triangular prateado

### 🥇 Ouro (Posição 3)
- Borda dourada com brilho intenso
- Indicador triangular dourado maior

### � Diamante (Posição 2)
- Borda ciano/azul com brilho diamante
- Emoji de diamante (�) na parte inferior
- Sem asas (removidas)

### 👑 Coroa (Posição 1)
- Borda multicolorida com gradiente animado
- Emoji de coroa (👑) dourada no topo
- Efeito de flutuação na coroa

## Como Usar

### 1. Componente Blade (Recomendado)

```blade
<!-- Uso básico -->
<x-ranking-avatar :user="$user" />

<!-- Com tamanhos diferentes -->
<x-ranking-avatar :user="$user" size="sm" />
<x-ranking-avatar :user="$user" size="lg" />
<x-ranking-avatar :user="$user" size="xl" />

<!-- Com classes adicionais -->
<x-ranking-avatar :user="$user" class="border-2 border-white shadow-lg" />

<!-- Com URL personalizada -->
<x-ranking-avatar :user="$user" src="{{ $customAvatarUrl }}" alt="Custom Avatar" />
```

### 2. HTML Manual

```blade
@php
    $rankingBorderClass = getRankingBorderClass($user);
    $shouldHaveWings = in_array($rankingBorderClass, ['crown', 'diamond']);
@endphp

<div class="ranking-avatar {{ $rankingBorderClass }}">
    @if($shouldHaveWings)
        <div class="wings"></div>
    @endif
    <img src="{{ $avatarUrl }}" alt="{{ $user->name }}" class="w-10 h-10 rounded-full object-cover">
</div>
```

### 3. Funções Helper

```php
// Obter classe de borda
$borderClass = getRankingBorderClass($user);

// Obter posição no ranking
$position = getUserRankingPosition($user);

// Obter informações completas
$rankingInfo = getRankingInfo($user);
// Retorna: ['position' => 1, 'border_class' => 'crown', 'tier' => 'Rei/Rainha', 'has_border' => true]
// Para 2º lugar: ['position' => 2, 'border_class' => 'diamond', 'tier' => 'Diamante', 'has_border' => true]

// Por ID do usuário (mais performático)
$borderClass = getRankingBorderClassById($userId);
```

## Tamanhos Disponíveis

- `xs`: 6x6 (w-6 h-6)
- `sm`: 8x8 (w-8 h-8)
- `md`: 10x10 (w-10 h-10) - padrão
- `lg`: 16x16 (w-16 h-16)
- `xl`: 24x24 (w-24 h-24)
- `2xl`: 32x32 (w-32 h-32)

## Animações

### Bronze
- Brilho suave alternado (3s)

### Prata
- Brilho médio alternado (2.5s)

### Ouro
- Brilho intenso alternado (2s)

### Diamante
- Brilho ciano intenso (1.5s)
- Bounce do emoji diamante (2s)

### Coroa
- Gradiente animado na borda (1s)
- Flutuação da coroa dourada (3s)

## Responsividade

O sistema é totalmente responsivo e se adapta a diferentes tamanhos de tela:

- Em dispositivos móveis, os elementos decorativos são ligeiramente menores
- As animações são otimizadas para performance
- As bordas mantêm proporções adequadas

## Performance

- As consultas de ranking são otimizadas
- Funções helper com cache interno
- CSS com animações GPU-aceleradas
- Componente Blade reutilizável

## Exemplos de Implementação

### Feed de Posts
```blade
<x-ranking-avatar :user="$post->user" size="md" />
```

### Comentários
```blade
<x-ranking-avatar :user="$comment->user" size="sm" />
```

### Perfil do Usuário
```blade
<x-ranking-avatar :user="$user" size="xl" class="border-4 border-white shadow-lg" />
```

### Ranking/Leaderboard
```blade
<x-ranking-avatar :user="$rankedUser" size="lg" />
```

## Customização

Para customizar as cores ou animações, edite o arquivo `resources/css/ranking-borders.css`.

### Exemplo de Customização
```css
/* Personalizar cor da borda ouro */
.ranking-avatar.gold::before {
    background: linear-gradient(45deg, #your-color-1, #your-color-2);
}
```
